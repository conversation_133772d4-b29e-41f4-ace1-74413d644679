from typing import Any, Dict, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import GitHubProjectRepo, UsageType, VersionControlSystem
from sqlalchemy.orm import Session

from src.error.base_error import BlitzyError


def get_github_project_repo_by_repo_and_branch(repo_id: str, base_branch: str,
                                               session: Optional[Session] = None) -> GitHubProjectRepo:
    """
    Fetches a GitHub project repository by repository ID and branch name.

    :param repo_id: The identifier of the GitHub repository.
    :type repo_id: str
    :param base_branch: The branch name in the GitHub repository.
    :type base_branch: str
    :param session: An optional database session.
    :type session: Optional[Session]
    :return: The matching GitHubProjectRepo instance or None if not found.
    :rtype: Optional[GitHubProjectRepo]
    """
    with get_db_session(session) as session:
        project_repo = session.query(GitHubProjectRepo).filter_by(
            repo_id=repo_id,
            branch_name=base_branch,
            usage_type=UsageType.SOURCE
        ).first()

        if project_repo and not session:
            session.expunge(project_repo)
        return project_repo


def get_github_project_repo_by_repo_and_org(repo_id: str, org_name: str,
                                               session: Optional[Session] = None) -> GitHubProjectRepo:
    """
    Fetches a GitHub project repository by repository ID and org name.

    :param repo_id: The identifier of the GitHub repository.
    :type repo_id: str
    :param org_name: The org name in the GitHub project repository.
    :type org_name: str
    :param session: An optional database session.
    :type session: Optional[Session]
    :return: The matching GitHubProjectRepo instance or None if not found.
    :rtype: Optional[GitHubProjectRepo]
    """

    with get_db_session(session) as session:
        project_repo = session.query(GitHubProjectRepo).filter_by(
            repo_id=repo_id,
            org_name=org_name,
            usage_type=UsageType.SOURCE
        ).filter(
            GitHubProjectRepo.azure_project_id.isnot(None),
            GitHubProjectRepo.azure_project_id != ''
        ).first()

        if project_repo and not session:
            session.expunge(project_repo)
        return project_repo


def update_github_project_repo_by_id(github_project_repo_id: str, update_payload: Dict[Any, Any],
                                     session: Optional[Session] = None):
    """
    Updates a GitHub project repository by its ID with the provided payload.

    :param github_project_repo_id: The ID of the GitHub project repository to update.
    :param update_payload: The payload containing fields to update.
    :param session: An optional database session. A new session will be created if not provided.
    :return: None
    :raises BlitzyError: If no record is found for the provided repository ID.
    """
    with get_db_session(session) as session:
        updated = session.query(GitHubProjectRepo).filter(GitHubProjectRepo.id == github_project_repo_id).update(
            update_payload)

        if not updated:
            error_message = f"No record found for id {github_project_repo_id}"
            logger.error(error_message)
            raise BlitzyError(error_message)


def get_github_project_repo_by_repo_id(repo_id: str,
                                       session: Optional[Session] = None) -> Optional[GitHubProjectRepo]:
    with get_db_session(session) as session:
        project_repo = session.query(GitHubProjectRepo).filter(GitHubProjectRepo.repo_id == repo_id).first()

        if project_repo and not session:
            session.expunge(project_repo)

        return project_repo
