from datetime import UTC, datetime
from typing import Optional

from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient
from common_models.db_client import get_db_session
from common_models.models import (AccessType, GithubInstallation,
                                  GitHubInstallationAccess,
                                  GithubInstallationStatus,
                                  PendingInstallationRequest,
                                  VersionControlSystem)
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm import Session
from tenacity import (retry, retry_if_exception_type, stop_after_attempt,
                      wait_exponential)

from src.api.models import SaveGithubInstallationInfo
from src.service.github_installation_access_service import \
    create_github_installation_access_record_for_user
from src.service.pending_installation_requests_service import \
    get_pending_installation_requests_by_user_id


def create_github_installation(user_id: str, status: GithubInstallationStatus, payload: SaveGithubInstallationInfo,
                               user_info: dict):
    """
    Create or update a GitHub installation for a given user with the provided status and payload.

    This function interacts with the database to either create a new GitHub installation or
    update an existing one based on the provided user information. The function ensures that
    the database session is properly managed and commits changes if no errors occur. In the
    event of an exception, the session is rolled back and the error is re-raised.

    :param user_id: Unique identifier for the user.
    :param status: Current status of the GitHub installation.
    :param payload: Detailed information to be saved or updated for the GitHub installation.
    :param user_info: User information received from the Github.
    :return: The created or updated GitHub installation object.
    """
    with get_db_session() as session:
        try:
            installation = try_create_or_update_github_installation(session, user_id, status, payload, user_info)
            session.commit()
            return installation
        except IntegrityError as e:
            session.rollback()
            raise e
        except SQLAlchemyError as e:
            session.rollback()
            raise e
        except Exception as e:
            # Handle any errors that occurred during commit
            session.rollback()
            raise e


@retry(
    retry=retry_if_exception_type(IntegrityError),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
def try_create_or_update_github_installation(session, user_id: str, status: GithubInstallationStatus,
                                             payload: SaveGithubInstallationInfo, user_info: dict):
    """
    Attempts to create or update a GitHub installation record. This function supports retry logic in the
    case of IntegrityError exceptions, ensuring that the operation is attempted multiple times with
    exponential backoff.

    If a record matching the given installation ID in the payload already exists, the record is updated.
    If no matching record is found, a new one is created and added to the database session.

    :param session: The database session used to query and/or persist the GitHub installation model.
    :param user_id: The identifier of the user associated with the GitHub installation.
    :param status: The status of the GitHub installation, represented by an enumeration.
    :param payload: The payload containing information to create or update the GitHub installation. It
                    includes attributes like installation ID and other metadata.
    :param user_info: User information received from the Github.
    :return: The resulting GitHub installation object, either updated or newly created, as stored in the
             database.
    """

    installation = None
    if payload.installationID:
        # Try to get existing record with lock
        installation = (
            session.query(GithubInstallation)
            .filter(GithubInstallation.installation_id == payload.installationID)
            .first()
        )
    else:
        # We don't get installation ID when user requests for the github app installation.
        installation = (
            session.query(GithubInstallation)
            .filter(GithubInstallation.requested_by == str(user_info["id"]),
                    GithubInstallation.status == GithubInstallationStatus.PENDING)
            .order_by(GithubInstallation.created_at.desc())
            .first()
        )

    if installation:
        # Update existing
        installation = update_github_installation_model(user_id, installation, payload.installationID, status,
                                                        payload, user_info, session)
    else:
        # Insert new.
        create_github_installation_records(user_id, payload, status, user_info, session)

    if status == GithubInstallationStatus.PENDING:
        # Create or update record in staging table for pending requests.
        logger.debug(f"Request received for pending installation for user {user_id}."
                     f" Create record in pending installation table.")
        insert_pending_installation_record(payload, installation, user_id, session)
    session.flush()
    return installation


def update_github_installation_model(user_id: str, installation: GithubInstallation, installation_id: str,
                                     status: GithubInstallationStatus, payload: SaveGithubInstallationInfo,
                                     user_info: dict, session: Session) -> GithubInstallation:
    """
    Updates the Github installation model with new information. This function allows
    modification of the installation details by updating its metadata, status, and
    user information. It returns the updated GithubInstallation object.

    :param user_id: The identifier for the associated user.
    :param installation: The current GithubInstallation object to be updated.
    :param installation_id: The unique identifier for the installation.
    :param status: The new status to be assigned to the installation.
    :param payload: Object containing additional setup information to be applied
        to the Github installation.
    :param user_info: User information received from the Github.
    :param session: Client session.
    :return: The updated GithubInstallation object containing the modified data.
    """
    payload_metadata = {
        "code": payload.code,
        "setup_action": payload.setupAction
    }

    installation.status = status
    installation.installation_id = installation_id
    installation.installation_id = payload.installationID
    installation.installation_metadata.update(payload_metadata)
    installation.status = status
    installation.user_id = user_id

    github_installation_access = create_github_installation_access_record_for_user(installation.id, user_id, session)
    logger.info(f"Created github installation access record for user {user_id} with id {github_installation_access.id}")

    status_check(installation, user_info)
    return installation


def create_github_installation_records(user_id: str, payload: SaveGithubInstallationInfo,
                                       status: GithubInstallationStatus, user_info: dict,
                                       session: Optional[Session] = None):
    """
    Creates a new GithubInstallation model using the provided payload, user ID, and
    an installation status.

    This function extracts relevant metadata from the payload and constructs a
    GithubInstallation instance. The metadata is packaged into a dictionary using
    the `code` and `setupAction` attributes from the provided payload.

    :param user_id: User id.
    :param payload: An instance of SaveGithubInstallationInfo.
    :param status: The status of the Github installation.
    :param user_info: User information received from the Github.
    :param session: Client session if any.
    """
    with get_db_session(session) as session:
        payload_metadata = {
            "code": payload.code,
            "setup_action": payload.setupAction
        }
        installation = GithubInstallation(
            installation_id=payload.installationID,
            installation_metadata=payload_metadata,
            status=status,
            user_id=user_id,
            # We use backend service only to create github records, because this approach is deprecated
            # azure and gitlab records created by archie-github-handler
            svc_type=VersionControlSystem.GITHUB.value,
        )

        status_check(installation, user_info)
        session.add(installation)
        session.flush()

        create_github_installation_access_record_for_user(installation.id, user_id, session)
        logger.info(f"Created github installation access record for user {user_id}")


def status_check(installation: GithubInstallation, user_info: dict):
    """
    Check the status of a given GithubInstallation and update its installed_at attribute if
    the installation is active.

    :param installation: The GithubInstallation instance whose status is to be checked.
    :param user_info: User information received from the Github.
    """
    if installation.status == GithubInstallationStatus.ACTIVE:
        installation.installed_at = datetime.now(tz=UTC)
    elif installation.status == GithubInstallationStatus.PENDING:
        installation.requires_approval = True
        installation.requested_by = str(user_info["id"])
        installation.requested_at = datetime.now(tz=UTC)


def insert_pending_installation_record(payload: SaveGithubInstallationInfo, github_installation: GithubInstallation,
                                       user_id: str, session: Optional[Session] = None):
    """
    Inserts or updates a pending GitHub installation record for a specific user.

    This function attempts to retrieve an existing pending installation record for
    the given user. If no such record exists, it creates a new installation record.
    If a record is found, it updates the existing record with the provided GitHub
    installation information. In case of an IntegrityError, indicating the record
    already exists, the function handles it gracefully without creating a duplicate
    record. A session can optionally be passed for database transactions.

    :param payload: Data with information about the GitHub installation.
    :param github_installation: Object containing details of the GitHub installation.
    :param user_id: The unique identifier associated with the user creating/updating
        the record.
    :param session: Optional database session for managing transactions; defaults
        to None.
    """
    try:
        pending_installation = get_pending_installation_requests_by_user_id(user_id, session)
        if not pending_installation:
            logger.info(f"No pending installation record found for user {user_id}. Creating new record.")
            create_pending_installation_record(payload, github_installation, user_id, session)
            return

        logger.info(f"Pending installation record found for user {user_id}. Updating existing record.")
        pending_installation.request_by = github_installation.requested_by
        pending_installation.user_id = user_id
        session.add(pending_installation)
    except IntegrityError:
        logger.info("Record for pending installation already exists. No need to create new record.")


def create_pending_installation_record(payload: SaveGithubInstallationInfo, github_installation: GithubInstallation,
                                       user_id: str, session: Optional[Session] = None):
    """
    Creates a new record for a pending GitHub installation request in the database using payload and
    github_installation object.

    :param payload: Contains the necessary data required to create a pending GitHub installation
        request. Specifically, this includes the authorization code for the request.
    :param github_installation: Represents the GitHub installation details required to populate
        the `requested_by` field of the pending installation record.
    :param user_id: User id.
    :param session: Session if any.
    """
    try:
        pending_installation = PendingInstallationRequest()
        pending_installation.request_by = github_installation.requested_by
        pending_installation.code = payload.code
        pending_installation.user_id = user_id
        session.add(pending_installation)
        session.flush()

        github_installation_access = GitHubInstallationAccess(
            integration_id=github_installation.id,
            entity_id=user_id,
            access_type=AccessType.USER,
            is_owner=True
        )
        session.add(github_installation_access)
        session.flush()
        session.commit()
    except IntegrityError:
        logger.info("Record for pending installation already exists. No need to create new record.")


def get_active_github_installation_by_user_id(user_id: str,
                                              session: Optional[str] = None) -> Optional[GithubInstallation]:
    """
    Retrieve the active GitHub installation for a user by user ID.

    :param user_id: The ID of the user whose GitHub installation is being retrieved.
    :param session: An optional database session.
    :return: The active GitHub installation associated with the user ID, or None if not found.
    """
    with get_db_session(session) as session:
        github_installation = (session.query(GithubInstallation)
                               .filter(GithubInstallation.user_id == user_id,
                                       GithubInstallation.status == GithubInstallationStatus.ACTIVE,
                                       GithubInstallation.svc_type == VersionControlSystem.GITHUB)
                               .order_by(GithubInstallation.created_at.desc())
                               .first())
        if github_installation and not session:
            session.expunge(github_installation)
        return github_installation


def get_active_installations_from_github_handler(user_id: str):
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/github/users/{user_id}/installations"
        )
        response.raise_for_status()
        return response.json()
