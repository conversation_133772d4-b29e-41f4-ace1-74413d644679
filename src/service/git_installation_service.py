from datetime import UTC, datetime
from typing import Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (AccessRole, AccessType, GithubInstallation,
                                  GitHubInstallationAccess,
                                  GithubInstallationStatus,
                                  GithubInstallationType, VersionControlSystem)
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm import Session
from tenacity import (retry, retry_if_exception_type, stop_after_attempt,
                      wait_exponential)

from src.service.github_installation_access_service import \
    create_github_installation_access_record_for_user


def create_new_git_installation(
        user_id: str, status: GithubInstallationStatus, installation_id: str,
        target_id: str, target_name: str, svc_type: VersionControlSystem, metadata: dict
) -> str:
    """
    Create or update a Git installation for a given user with the provided status and type(azure or github).
    Also creates a record for the user's access to the installation.

    :param user_id: Unique identifier for the user.
    :param status: Current status of the GitHub installation.
    :param installation_id: The installation id.
    :param target_id: The id of organization or user installation was performed for
    :param target_name: The name of organization or user installation was performed for
    :param svc_type: The type of service(azure or github) for which the installation was performed.
    :param metadata: Additional metadata to be saved with the installation, such as user_info.
    :return: The created or updated Git installation id.
    """
    with get_db_session() as session:
        try:
            installation = create_new_git_installation_records(
                user_id=user_id, status=status, installation_id=installation_id,
                target_name=target_name, target_id=target_id, installation_type=GithubInstallationType.USER,
                svc_type=svc_type, metadata=metadata, session=session)
            session.commit()
            return installation.id
        except IntegrityError as e:
            session.rollback()
            raise e
        except SQLAlchemyError as e:
            session.rollback()
            raise e
        except Exception as e:
            # Handle any errors that occurred during commit
            session.rollback()
            raise e


@retry(
    retry=retry_if_exception_type(IntegrityError),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
def create_new_git_installation_records(
    user_id: str, status: GithubInstallationStatus, installation_id: str,
    target_name: str, target_id: str, installation_type: GithubInstallationType,
    svc_type: VersionControlSystem,
    metadata: dict, session: Session,
) -> GithubInstallation:
    """
    Creates a new GithubInstallation and GitHubInstallationAccess records in the database if they don't already exist.
    """
    active_installation = get_active_git_installation_by_id_and_user_id(installation_id, user_id, session)

    if active_installation:
        logger.info(
            f"We have already existing active github installation {active_installation.id} record "
            f"for user {user_id}, will not create new record."
        )
    else:
        logger.info(f"Creating new github installation record for user {user_id}")
        active_installation = GithubInstallation(
            installation_id=installation_id,
            installation_metadata=metadata,
            installation_type=installation_type,
            status=status,
            target_name=target_name,
            target_id=target_id,
            user_id=user_id,
            svc_type=svc_type,
        )

        status_check(active_installation, user_id)
        session.add(active_installation)
        session.flush()

        create_github_installation_access_record_for_user(active_installation.id, user_id, session)
        logger.info(f"Created github installation access record for user {user_id}")
    return active_installation


def status_check(installation: GithubInstallation, user_id: str):
    """
    Check the status of a given GithubInstallation and update its installed_at attribute if
    the installation is active.

    :param installation: The GithubInstallation instance whose status is to be checked.
    :param user_id: The user id of the user who installed the app.
    """
    if installation.status == GithubInstallationStatus.ACTIVE:
        installation.installed_at = datetime.now(UTC)
    elif installation.status == GithubInstallationStatus.PENDING:
        installation.requires_approval = True
        installation.requested_by = user_id
        installation.requested_at = datetime.now(UTC)


def create_git_installation_access_record(github_installation: GithubInstallation,
                                          user_id: str, session: Optional[Session] = None):
    """
    Creates a new record for a pending GitHub installation request in the database using payload and
    github_installation object.

    :param github_installation: Represents the GitHub installation details required to populate
        the `requested_by` field of the pending installation record.
    :param user_id: User id.
    :param session: Session if any.
    """
    try:
        github_installation_access = GitHubInstallationAccess(
            integration_id=github_installation.id,
            entity_id=user_id,
            access_type=AccessType.USER,
            role=AccessRole.OWNER,
            is_owner=True
        )
        session.add(github_installation_access)
        session.flush()
    except IntegrityError:
        logger.info("Record for pending installation already exists. No need to create new record.")


def get_active_git_installation_by_id_and_user_id(
        installation_id: str,
        user_id: str,
        session: Session) -> Optional[GithubInstallation]:
    installation = (
        session.query(GithubInstallation)
        .filter(
            GithubInstallation.installation_id == installation_id,
            GithubInstallation.user_id == user_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
        ).first()
    )
    return installation


def get_active_git_integration_by_installation_and_svc_type(
        installation_id: str,
        svc_type: VersionControlSystem,
        session: Optional[Session] = None
) -> Optional[GithubInstallation]:
    """
    Get the latest active git installation info by installation id and svc_type.
    :param installation_id: which is tenant_id for azure.
    :param svc_type: Version control system we want to get installation info for, e.g. GitHub, GitLab, Bitbucket...
    :param session: Session if any.
    :return: GithubInstallation model if found or None.
    """
    with get_db_session(session) as session:
        github_installation = (session.query(GithubInstallation)
                                      .filter(GithubInstallation.installation_id == installation_id,
                                              GithubInstallation.svc_type == svc_type,
                                              GithubInstallation.status == GithubInstallationStatus.ACTIVE)
                                      .order_by(GithubInstallation.created_at.desc())
                                      .first())
        if not github_installation:
            return None

        if github_installation and not session:
            session.expunge(github_installation)
        return github_installation
