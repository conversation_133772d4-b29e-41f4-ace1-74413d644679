"""
Azure DevOps user-related API endpoints for archie-github-handler.

This module contains Azure DevOps-specific user operations that directly implement
Azure DevOps functionality without service type detection.
"""

from blitzy_utils.logger import logger
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response

from src.api.models import (
    AzureOrgBasicList,
    AzureProjectList,
    RepositoryList,
    BranchList,
)
from src.api.utils.azure_utils import (
    get_azure_orgs_by_installations,
    get_azure_projects_by_user_and_org,
    get_azure_repos_by_user_and_org,
    get_azure_repos_by_user_org_and_project,
    get_azure_branches_by_repo_id,
    get_azure_branches_by_repo_with_context,
)
from src.error.errors import ResourceNotFound
from src.service.user_service import get_user_by_id

# Create Azure users blueprint
azure_users_bp = Blueprint("azure_users", __name__, url_prefix="/v1/azure/users")


@azure_users_bp.route("/<user_id>/organizations", methods=["GET"])
@flask_pydantic_response
def get_azure_user_organizations(user_id: str):
    """
    Get Azure DevOps organizations for a specific user.
    
    This endpoint only handles Azure DevOps organizations.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Since this is /v1/azure/* endpoint, we know it's Azure DevOps
    # No need to check installation service type
    org_list = get_azure_orgs_by_installations(user_info)
    response = AzureOrgBasicList(organizations=org_list)

    return response, 200


@azure_users_bp.route("/<user_id>/organizations/<org_id>/projects", methods=["GET"])
@flask_pydantic_response
def get_azure_user_projects(user_id: str, org_id: str):
    """
    Get Azure DevOps projects for a specific user and organization.
    
    This endpoint only handles Azure DevOps projects.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get Azure DevOps projects
    project_list = get_azure_projects_by_user_and_org(user_info, org_id)
    response = AzureProjectList(projects=project_list)

    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/projects/<project_id>/repositories",
    methods=["GET"],
)
@flask_pydantic_response
def get_azure_user_repositories(user_id: str, org_id: str, project_id: str):
    """
    Get Azure DevOps repositories for a specific user, organization, and project.
    
    This endpoint only handles Azure DevOps repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get Azure DevOps repositories
    repo_list = get_azure_repos_by_user_org_and_project(user_info, org_id, project_id)
    response = RepositoryList(repositories=repo_list)

    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/repositories/<repo_id>/branches", methods=["GET"]
)
@flask_pydantic_response
def get_azure_user_branches_unified(user_id: str, org_id: str, repo_id: str):
    """
    Get Azure DevOps branches for a specific user, organization, and repository.
    
    This endpoint derives project_id automatically from the database.
    This endpoint only handles Azure DevOps branches.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Use get_azure_branches_by_repo_with_context which handles project derivation
    branches_list = get_azure_branches_by_repo_with_context(
        user_info, org_id, None, repo_id
    )
    response = BranchList(branches=branches_list)

    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/projects/<project_id>/repositories/<repo_id>/branches",
    methods=["GET"],
)
@flask_pydantic_response
def get_azure_user_branches_with_project(
    user_id: str, org_id: str, project_id: str, repo_id: str
):
    """
    Get Azure DevOps branches for a specific user, organization, project, and repository.
    
    This endpoint uses explicit project_id in the hierarchical structure.
    This endpoint only handles Azure DevOps branches.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get Azure DevOps branches with explicit project_id
    branches_list = get_azure_branches_by_repo_id(
        user_info, org_id, project_id, repo_id
    )
    response = BranchList(branches=branches_list)

    return response, 200
