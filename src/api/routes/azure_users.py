"""
Azure DevOps user-related API endpoints.

This module contains Azure DevOps-specific user operations that are separate from GitHub functionality.
"""

from blitzy_utils.logger import logger
from common_models.models import VersionControlSystem
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response

from src.api.utils.azure_utils import (
    map_azure_org_list_to_pydantic,
    map_azure_project_list_to_pydantic,
    map_azure_repo_list_to_pydantic,
    map_azure_branches_to_pydantic,
)
from src.error.errors import ResourceNotFound
from src.service.azure_service import (
    get_azure_organizations_by_user,
    get_azure_projects_by_user_and_org,
    get_azure_repositories_by_user_and_org,
    get_azure_repositories_by_user_org_and_project,
    get_azure_branches_by_repo_id,
)
from src.service.github_integration_service import get_github_installations_by_user
from src.service.user_service import get_user_by_id

# Create Azure users blueprint
azure_users_bp = Blueprint("azure_users", __name__, url_prefix="/v1/azure/users")


@azure_users_bp.route("/<user_id>/organizations", methods=["GET"])
@flask_pydantic_response
def get_azure_user_organizations(user_id: str):
    """
    Get Azure DevOps organizations for a specific user.

    This endpoint only handles Azure DevOps organizations.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get Azure DevOps installations only
    installation_list = get_github_installations_by_user(user_id)
    if not installation_list:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Filter for Azure DevOps installations only
    azure_installations = [
        inst
        for inst in installation_list
        if inst.svc_type == VersionControlSystem.AZURE_DEVOPS
    ]

    if not azure_installations:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Use Azure DevOps service to get organizations
    org_list = get_azure_organizations_by_user(user_info)
    response = map_azure_org_list_to_pydantic(org_list)

    return response, 200


@azure_users_bp.route("/<user_id>/organizations/<org_id>/projects", methods=["GET"])
@flask_pydantic_response
def get_azure_user_projects(user_id: str, org_id: str):
    """
    Get Azure DevOps projects for a specific user and organization.

    This endpoint only handles Azure DevOps projects.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get Azure DevOps installations only
    installation_list = get_github_installations_by_user(user_id)
    if not installation_list:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Filter for Azure DevOps installations only
    azure_installations = [
        inst
        for inst in installation_list
        if inst.svc_type == VersionControlSystem.AZURE_DEVOPS
    ]

    if not azure_installations:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Use Azure DevOps service to get projects
    project_list = get_azure_projects_by_user_and_org(user_info, org_id)
    response = map_azure_project_list_to_pydantic(project_list)

    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/projects/<project_id>/repositories",
    methods=["GET"],
)
@flask_pydantic_response
def get_azure_user_repositories(user_id: str, org_id: str, project_id: str):
    """
    Get Azure DevOps repositories for a specific user, organization, and project.

    This endpoint only handles Azure DevOps repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get Azure DevOps installations only
    installation_list = get_github_installations_by_user(user_id)
    if not installation_list:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Filter for Azure DevOps installations only
    azure_installations = [
        inst
        for inst in installation_list
        if inst.svc_type == VersionControlSystem.AZURE_DEVOPS
    ]

    if not azure_installations:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Use Azure DevOps service to get repositories
    repo_list = get_azure_repositories_by_user_org_and_project(
        user_info, org_id, project_id
    )
    response = map_azure_repo_list_to_pydantic(repo_list)

    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/repositories/<repo_id>/branches", methods=["GET"]
)
@flask_pydantic_response
def get_azure_user_branches_unified(user_id: str, org_id: str, repo_id: str):
    """
    Get Azure DevOps branches for a specific user, organization, and repository.

    This endpoint derives project_id automatically from the database.
    This endpoint only handles Azure DevOps branches.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get Azure DevOps installations only
    installation_list = get_github_installations_by_user(user_id)
    if not installation_list:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Filter for Azure DevOps installations only
    azure_installations = [
        inst
        for inst in installation_list
        if inst.svc_type == VersionControlSystem.AZURE_DEVOPS
    ]

    if not azure_installations:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Use Azure DevOps service to get branches
    branches_list = get_azure_branches_by_repo_id(user_info, repo_id)
    response = map_azure_branches_to_pydantic(branches_list)

    return response, 200


@azure_users_bp.route(
    "/<user_id>/organizations/<org_id>/projects/<project_id>/repositories/<repo_id>/branches",
    methods=["GET"],
)
@flask_pydantic_response
def get_azure_user_branches_with_project(
    user_id: str, org_id: str, project_id: str, repo_id: str
):
    """
    Get Azure DevOps branches for a specific user, organization, project, and repository.

    This endpoint uses explicit project_id in the hierarchical structure.
    This endpoint only handles Azure DevOps branches.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get Azure DevOps installations only
    installation_list = get_github_installations_by_user(user_id)
    if not installation_list:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Filter for Azure DevOps installations only
    azure_installations = [
        inst
        for inst in installation_list
        if inst.svc_type == VersionControlSystem.AZURE_DEVOPS
    ]

    if not azure_installations:
        raise ResourceNotFound(
            f"No Azure DevOps installations found for user {user_id}"
        )

    # Use Azure DevOps service to get branches
    branches_list = get_azure_branches_by_repo_id(user_info, repo_id)
    response = map_azure_branches_to_pydantic(branches_list)

    return response, 200


# Azure handler functions that call archie-github-handler /v1/azure/* endpoints


def get_organizations_from_azure_handler(user_id: str):
    """
    Call archie-github-handler service to get Azure DevOps organizations for a user.
    """
    with ServiceClient() as client:
        response = client.get("github", f"/v1/azure/users/{user_id}/organizations")
        response.raise_for_status()
        return response.json()


def get_projects_from_azure_handler(user_id: str, org_id: str):
    """
    Call archie-github-handler service to get Azure DevOps projects for an organization.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/projects",
        )
        response.raise_for_status()
        return response.json()


def get_repositories_from_azure_handler(user_id: str, org_id: str, project_id: str):
    """
    Call archie-github-handler service to get repositories from Azure DevOps project.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/projects/{project_id}/repositories",
        )
        response.raise_for_status()
        return response.json()


def get_branches_from_azure_handler_unified(user_id: str, org_id: str, repo_id: str):
    """
    Call archie-github-handler service to get branches from Azure DevOps repository.
    This uses the endpoint that derives project_id automatically from the database.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/repositories/{repo_id}/branches",
        )
        response.raise_for_status()
        return response.json()


def get_branches_from_azure_handler_with_project(
    user_id: str, org_id: str, project_id: str, repo_id: str
):
    """
    Call archie-github-handler service to get branches from Azure DevOps repository.
    This uses the hierarchical endpoint with explicit project_id.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/projects/{project_id}/repositories/{repo_id}/branches",
        )
        response.raise_for_status()
        return response.json()
