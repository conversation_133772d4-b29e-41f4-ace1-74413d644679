"""
GitHub user-related API endpoints.

This module contains GitHub-specific user operations that are separate from Azure DevOps functionality.
"""

from blitzy_utils.logger import logger
from flask import Blueprint, jsonify
from common_models.models import VersionControlSystem
from flask_utils.decorators import flask_pydantic_response, validate_request
from flask_utils.models_config.model_utils import map_to_model

from src.api.models import (
    Branch,
    BranchList,
    DefaultBranchOutput,
    GithubOrgBasic,
    GithubOrgBasicList,
    PRActionInput,
    Repository,
    RepositoryList,
)
from src.api.utils.github_utils import (
    get_branches_by_repo_id,
    get_orgs_by_installations,
    get_repo_by_id,
    get_repos_by_user,
    map_branches_to_pydantic,
    map_org_list_to_pydantic,
    map_repo_list_to_pydantic,
)
from src.error.errors import ResourceNotFound
from src.github.github_app_service import GithubAppService
from src.service.github_installation_access_service import (
    get_active_github_installation_by_repo_id,
)
from src.service.github_integration_service import (
    get_github_installation_by_user_and_target_name,
    get_github_installations_by_user,
)
from src.service.user_service import get_user_by_id

# Create GitHub users blueprint
github_users_bp = Blueprint("github_users", __name__, url_prefix="/v1/github/users")


@github_users_bp.route("/<user_id>/organizations", methods=["GET"])
@flask_pydantic_response
def get_github_user_organizations(user_id: str):
    """
    Get GitHub organizations for a specific user.

    This endpoint only handles GitHub organizations.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub installations only
    installation_list = get_github_installations_by_user(user_id)
    if not installation_list:
        raise ResourceNotFound(f"No GitHub installations found for user {user_id}")

    # Filter for GitHub installations only
    github_installations = [
        inst
        for inst in installation_list
        if inst.svc_type == VersionControlSystem.GITHUB
    ]

    if not github_installations:
        raise ResourceNotFound(f"No GitHub installations found for user {user_id}")

    # Get organizations from GitHub installations
    org_list = get_orgs_by_installations(github_installations)
    response = map_org_list_to_pydantic(org_list)

    return response, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories", methods=["GET"]
)
@flask_pydantic_response
def get_github_user_repositories(user_id: str, account_name: str):
    """
    Get GitHub repositories for a specific user and organization.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub installation only
    installation = get_github_installation_by_user_and_target_name(
        user_id, account_name
    )
    if not installation:
        raise ResourceNotFound(
            f"GitHub installation for user {user_id} and org {account_name} not found."
        )

    # Ensure this is a GitHub installation
    if installation.svc_type != VersionControlSystem.GITHUB:
        raise ResourceNotFound(
            f"GitHub installation not found for user {user_id} and org {account_name}."
        )

    # Use GitHub service to get repositories
    repo_list = get_repos_by_user(user_info, account_name)
    response = map_repo_list_to_pydantic(repo_list)

    return response, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories/<repo_id>/branches",
    methods=["GET"],
)
@flask_pydantic_response
def get_github_user_branches(user_id: str, account_name: str, repo_id: str):
    """
    Get GitHub branches for a specific user, organization, and repository.

    This endpoint only handles GitHub branches.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub installation only
    installation = get_github_installation_by_user_and_target_name(
        user_id, account_name
    )
    if not installation:
        raise ResourceNotFound(
            f"GitHub installation for user {user_id} and org {account_name} not found."
        )

    # Ensure this is a GitHub installation
    if installation.svc_type != VersionControlSystem.GITHUB:
        raise ResourceNotFound(
            f"GitHub installation not found for user {user_id} and org {account_name}."
        )

    # Use GitHub service to get branches
    branch_list = get_branches_by_repo_id(user_info, repo_id)
    if not branch_list:
        raise ResourceNotFound(f"Branches not found for GitHub repo {repo_id}")

    output = map_branches_to_pydantic(branch_list)
    return output, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories/<repo_id>", methods=["GET"]
)
@flask_pydantic_response
def get_github_user_repository(user_id: str, account_name: str, repo_id: str):
    """
    Get GitHub repository details for a specific user, organization, and repository.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub installation only
    installation = get_github_installation_by_user_and_target_name(
        user_id, account_name
    )
    if not installation:
        raise ResourceNotFound(
            f"GitHub installation for user {user_id} and org {account_name} not found."
        )

    # Ensure this is a GitHub installation
    if installation.svc_type != VersionControlSystem.GITHUB:
        raise ResourceNotFound(
            f"GitHub installation not found for user {user_id} and org {account_name}."
        )

    # Use GitHub service to get repository details
    repo_response = get_repo_by_id(user_info, repo_id)
    if not repo_response:
        raise ResourceNotFound(f"GitHub repository {repo_id} not found")

    response = Repository(**repo_response)
    return response, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories/<repo_id>/default/branch",
    methods=["GET"],
)
@flask_pydantic_response
def get_github_user_default_branch(user_id: str, account_name: str, repo_id: str):
    """
    Get GitHub default branch for a specific user, organization, and repository.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub installation only
    installation = get_github_installation_by_user_and_target_name(
        user_id, account_name
    )
    if not installation:
        raise ResourceNotFound(
            f"GitHub installation for user {user_id} and org {account_name} not found."
        )

    # Ensure this is a GitHub installation
    if installation.svc_type != VersionControlSystem.GITHUB:
        raise ResourceNotFound(
            f"GitHub installation not found for user {user_id} and org {account_name}."
        )

    # Use GitHub service to get default branch
    github_service = GithubAppService()
    branch_output = github_service.get_default_branch(
        repo_id, installation.installation_id
    )
    response = DefaultBranchOutput(**branch_output)

    return response, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories/<repo_id>/branch/<path:branch_name>/head/commit",
    methods=["GET"],
)
@flask_pydantic_response
def get_github_user_branch_head_commit(
    user_id: str, account_name: str, repo_id: str, branch_name: str
):
    """
    Get GitHub branch head commit for a specific user, organization, repository, and branch.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub installation only
    installation = get_github_installation_by_user_and_target_name(
        user_id, account_name
    )
    if not installation:
        raise ResourceNotFound(
            f"GitHub installation for user {user_id} and org {account_name} not found."
        )

    # Ensure this is a GitHub installation
    if installation.svc_type != VersionControlSystem.GITHUB:
        raise ResourceNotFound(
            f"GitHub installation not found for user {user_id} and org {account_name}."
        )

    # Use GitHub service to get branch head commit
    github_service = GithubAppService()
    branch_commit_output = github_service.get_branch_head_commit(
        repo_id, installation.installation_id, branch_name
    )

    if not branch_commit_output:
        raise ResourceNotFound(f"Commit information not found for {branch_name}")

    return branch_commit_output, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories/<repo_id>/pr/<pr_number>/action",
    methods=["POST"],
)
@validate_request(PRActionInput)
@flask_pydantic_response
def post_github_user_pr_action(
    user_id: str,
    account_name: str,
    repo_id: str,
    pr_number: str,
    payload: PRActionInput,
):
    """
    Perform action on GitHub pull request for a specific user, organization, repository, and PR.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub installation only
    installation = get_github_installation_by_user_and_target_name(
        user_id, account_name
    )
    if not installation:
        raise ResourceNotFound(
            f"GitHub installation for user {user_id} and org {account_name} not found."
        )

    # Ensure this is a GitHub installation
    if installation.svc_type != VersionControlSystem.GITHUB:
        raise ResourceNotFound(
            f"GitHub installation not found for user {user_id} and org {account_name}."
        )

    # Use GitHub service to perform PR action
    from src.github.github_app_connection import GithubAppConnection

    github_service = GithubAppConnection()
    pr_action_status = github_service.manage_pull_request(
        user_info.id,
        int(pr_number),
        payload.action.value,
        "merge",
        installation_id=installation.installation_id,
        repo_id=repo_id,
    )

    return pr_action_status, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories/<repo_id>/pr/<pr_number>/status",
    methods=["GET"],
)
@flask_pydantic_response
def get_github_user_pr_status(
    user_id: str, account_name: str, repo_id: str, pr_number: str
):
    """
    Get GitHub pull request status for a specific user, organization, repository, and PR.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub installation only
    installation = get_github_installation_by_user_and_target_name(
        user_id, account_name
    )
    if not installation:
        raise ResourceNotFound(
            f"GitHub installation for user {user_id} and org {account_name} not found."
        )

    # Ensure this is a GitHub installation
    if installation.svc_type != VersionControlSystem.GITHUB:
        raise ResourceNotFound(
            f"GitHub installation not found for user {user_id} and org {account_name}."
        )

    # Convert pr_number to int for validation
    try:
        pr_number_int = int(pr_number)
    except ValueError:
        raise ResourceNotFound(f"Invalid PR number: {pr_number}")

    # Use GitHub service to get PR status
    github_service = GithubAppService()
    # Get the GitHub installation for this repository
    github_repo_installation = get_active_github_installation_by_repo_id(repo_id)
    if not github_repo_installation:
        raise ResourceNotFound(
            f"No active GitHub installation found for repo_id: {repo_id}"
        )

    pr_status = github_service.get_pr_status_by_repo_id_and_pr_number(
        repo_id, pr_number_int, github_repo_installation.installation_id
    )

    if not pr_status:
        raise ResourceNotFound(
            f"PR with number {pr_number} not found for repository {repo_id}"
        )

    return jsonify({"status": pr_status}), 200
