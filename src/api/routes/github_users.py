"""
GitHub user-related API endpoints for archie-github-handler.

This module contains GitHub-specific user operations that directly implement
GitHub functionality without service type detection.
"""

from blitzy_utils.logger import logger
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response

from src.api.models import (
    GithubOrgBasicList,
    RepositoryList,
    BranchList,
    DefaultBranchOutput,
    PRActionInput,
)
from src.api.utils.github_utils import (
    get_orgs_by_installations,
    get_repos_by_installation,
    get_branches_by_repo_id,
    get_repo_by_id,
)
from src.error.errors import ResourceNotFound
from src.github.github_app_service import GithubAppService
from src.service.github_installation_access_service import (
    get_github_installations_by_user,
    get_active_github_installation_by_repo_id,
)
from src.service.user_service import get_user_by_id

# Create GitHub users blueprint
github_users_bp = Blueprint("github_users", __name__, url_prefix="/v1/github/users")


@github_users_bp.route("/<user_id>/organizations", methods=["GET"])
@flask_pydantic_response
def get_github_user_organizations(user_id: str):
    """
    Get GitHub organizations for a specific user.

    This endpoint only handles GitHub organizations.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Since this is /v1/github/* endpoint, we know it's GitHub
    # No need to check installation service type
    installation_list = get_github_installations_by_user(user_id)
    if not installation_list:
        raise ResourceNotFound(f"No installations found for user {user_id}")

    # Get GitHub organizations
    org_list = get_orgs_by_installations(installation_list)
    response = GithubOrgBasicList(organizations=org_list)

    return response, 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories", methods=["GET"])
@flask_pydantic_response
def get_github_user_repositories(user_id: str, account_name: str):
    """
    Get GitHub repositories for a specific user and organization.
    
    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Since this is /v1/github/* endpoint, we know it's GitHub
    # No need to check installation service type
    installation_list = get_github_installations_by_user(user_id)
    if not installation_list:
        raise ResourceNotFound(f"No installations found for user {user_id}")

    # Get GitHub repositories
    repo_list = get_repos_by_installation(installation_list, account_name)
    response = RepositoryList(repositories=repo_list)

    return response, 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories/<repo_id>/branches", methods=["GET"])
@flask_pydantic_response
def get_github_user_branches(user_id: str, account_name: str, repo_id: str):
    """
    Get GitHub branches for a specific user, organization, and repository.
    
    This endpoint only handles GitHub branches.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub branches
    branches_list = get_branches_by_repo_id(user_info, repo_id)
    response = BranchList(branches=branches_list)

    return response, 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories/<repo_id>", methods=["GET"])
@flask_pydantic_response
def get_github_user_repository(user_id: str, account_name: str, repo_id: str):
    """
    Get GitHub repository details for a specific user, organization, and repository.
    
    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get GitHub repository
    repo = get_repo_by_id(user_info, repo_id)
    
    return repo, 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories/<repo_id>/default/branch", methods=["GET"])
@flask_pydantic_response
def get_github_user_default_branch(user_id: str, account_name: str, repo_id: str):
    """
    Get GitHub default branch for a specific user, organization, and repository.
    
    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get active installation for this repository
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise ResourceNotFound(f"No active installation found for repository {repo_id}")

    # Get default branch using GitHub service
    github_service = GithubAppService(installation)
    default_branch = github_service.get_default_branch(repo_id)
    
    response = DefaultBranchOutput(default_branch=default_branch)
    return response, 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories/<repo_id>/branch/<branch_name>/head/commit", methods=["GET"])
@flask_pydantic_response
def get_github_user_branch_head_commit(user_id: str, account_name: str, repo_id: str, branch_name: str):
    """
    Get GitHub branch head commit for a specific user, organization, repository, and branch.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get active installation for this repository
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise ResourceNotFound(f"No active installation found for repository {repo_id}")

    # Get branch head commit using GitHub service
    github_service = GithubAppService(installation)
    commit = github_service.get_branch_head_commit(repo_id, branch_name)

    return commit, 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories/<repo_id>/pr/<pr_number>/action", methods=["POST"])
@flask_pydantic_response
def post_github_user_pr_action(user_id: str, account_name: str, repo_id: str, pr_number: str, body: PRActionInput):
    """
    Perform GitHub PR action for a specific user, organization, repository, and PR.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get active installation for this repository
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise ResourceNotFound(f"No active installation found for repository {repo_id}")

    # Perform PR action using GitHub service
    github_service = GithubAppService(installation)
    result = github_service.perform_pr_action(repo_id, pr_number, body.action)

    return result, 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories/<repo_id>/pr/<pr_number>/status", methods=["GET"])
@flask_pydantic_response
def get_github_user_pr_status(user_id: str, account_name: str, repo_id: str, pr_number: str):
    """
    Get GitHub PR status for a specific user, organization, repository, and PR.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get active installation for this repository
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise ResourceNotFound(f"No active installation found for repository {repo_id}")

    # Get PR status using GitHub service
    github_service = GithubAppService(installation)
    status = github_service.get_pr_status(repo_id, pr_number)

    return status, 200
