import hashlib
import hmac
from http.client import HTT<PERSON>Exception

from blitzy_utils.logger import logger
from flask import Blueprint, jsonify, request

from src.api.handlers.installation_event_handler import \
    handle_installation_event
from src.api.handlers.pr_event_handler import handle_pr_event
from src.api.handlers.repository_event_handler import handle_repository_event
from src.api.routes.operations import operations_bp
from src.api.routes.repositories import repositories_bp
from src.api.routes.secret_manager import secret_bp
from src.api.routes.users import users_bp
from src.consts import GITHUB_WEBHOOK_SECRET

SIGNATURE_HEADER_KEY = "X-Hub-Signature-256"
github_bp = Blueprint("github_bp", __name__, url_prefix="/v1/github")

github_bp.register_blueprint(users_bp)
github_bp.register_blueprint(secret_bp)
github_bp.register_blueprint(repositories_bp)
github_bp.register_blueprint(operations_bp)


@github_bp.route("/webhook", methods=["POST"])
def webhook():
    # Print all headers
    signature = request.headers.get(SIGNATURE_HEADER_KEY)

    verify_signature(request.data, signature)
    logger.debug("Verified signature.")

    event_type = request.headers.get("X-GitHub-Event")
    event_handler_factory(event_type, payload=request.json)

    return jsonify({"status": "success"}), 200


def verify_signature(payload_body: bytes, signature_header: str):
    """
    Verify that the payload was sent from GitHub by validating SHA256. Raise and return 403 if not authorized.

    :param payload_body: The original request body to verify.
    :param signature_header: The signature header received from GitHub.
    """
    if not signature_header:
        raise HTTPException(status_code=403, detail=f"{SIGNATURE_HEADER_KEY} header is missing!")

    expected_signature = generate_expected_signature(payload_body, GITHUB_WEBHOOK_SECRET)

    if not hmac.compare_digest(expected_signature, signature_header):
        raise HTTPException(status_code=403, detail="Signature mismatch! The request could not be verified.")


def generate_expected_signature(payload_body: bytes, secret: str) -> str:
    """Generate the expected HMAC SHA256 signature."""
    generated_hash = hmac.new(secret.encode("utf-8"), msg=payload_body, digestmod=hashlib.sha256)
    return "sha256=" + generated_hash.hexdigest()


def event_handler_factory(event: str, payload: dict):
    """Factory function to create event handlers."""
    logger.info(f"Handling github event {event}")
    if event == "installation":
        handle_installation_event(payload)
    elif event == "repository":
        handle_repository_event(payload)
    elif event == "pull_request":
        handle_pr_event(payload)
    else:
        logger.warning(f"No handler for event {event}")
        logger.debug(f"Payload: {payload}")
