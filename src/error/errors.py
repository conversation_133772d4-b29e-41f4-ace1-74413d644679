import http

from blitzy_utils.service_client import create_service_error_handler
from flask_utils.base_error import BaseError

check_service_error = create_service_error_handler(BaseError)


class ProjectNotFoundError(BaseError):
    """Failed to find project."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class ProjectUpdateError(BaseError):
    """Failed to update project."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class SoftwareRequirementUpdateError(BaseError):
    """Failed to update software requirement."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class TechnicalSpecificationUpdateError(BaseError):
    """Failed to update technical specification."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class TechnicalSpecificationNotFoundError(BaseError):
    """Failed to find technical specification."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class CodeGenerationUpdateError(BaseError):
    """Failed to update code generation table."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class UserError(BaseError):
    """Base class for all user-related errors"""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class InvalidUserCredentials(BaseError):
    """Raised when wrong user credentials are provided"""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class UserCreationError(BaseError):
    """Raised when user creation fails"""

    def __init__(self, message: str = "Failed to create user", status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class UserAlreadyExistsError(BaseError):
    """Raised when attempting to create a user that already exists"""

    def __init__(self, user_id: str):
        message = f"User with ID {user_id} already exists"
        super().__init__(message, status_code=409)  # 409 Conflict


class FirebaseUserNotFoundError(BaseError):
    """Raised when firebase user not found."""

    def __init__(self, message: str = "User not found in firebase", status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class UserValidationError(BaseError):
    """Raised when user data validation fails"""

    def __init__(self, message: str = "Invalid user data"):
        super().__init__(message, status_code=400)  # 400 Bad Request


class UserAuthProviderError(BaseError):
    """Raised when there's an error with the auth provider"""

    def __init__(self, message: str = "Error with authentication provider"):
        super().__init__(message, status_code=502)  # 502 Bad Gateway


class UserDatabaseError(BaseError):
    """Raised when there's a database error during user creation"""

    def __init__(self, message: str = "Database error during user creation"):
        super().__init__(message, status_code=500)


class JobNotCompleteError(BaseError):
    """When Job is not ready."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class JobInProgressError(BaseError):
    """When Job is not ready."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class TokenError(BaseError):
    """Base class for all token-related errors"""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class AuthorizationTokenMissing(BaseError):
    """Raised when authorization token is missing"""

    def __init__(self, message: str = "Authorization token missing", status_code: int = 401):
        super().__init__(message, status_code)


class FileTooLargeError(BaseError):
    """File too large error."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class FlaskFileNotFound(BaseError):
    """Flask file not found error."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class FailedToUploadFileError(BaseError):
    """Failed to upload file error."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class UserUpdateError(BaseError):
    """Failed to update user information."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class SubscriptionNotFoundError(BaseError):
    """Failed to find subscription details."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class GitHubIntegrationNotFoundError(BaseError):
    """Failed to find GitHub integration."""

    def __init__(self, message: str = "GitHub integration not found", status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class GitHubIntegrationError(BaseError):
    """GitHub integration error."""

    def __init__(self, message: str = "GitHub integration not found", status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class InvalidSubscriptionError(BaseError):
    """Invalid subscription error."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class JobLimitExhaustedError(BaseError):
    """Raised when the job creation limit is exceeded."""

    def __init__(self, message: str = "Job creation limit exhausted", status_code: int = 429):
        super().__init__(message=message, status_code=status_code)


class TrialJobLimitExhaustedError(BaseError):
    """Raised when the overall system trial job run is exhausted."""

    def __init__(self, message: str = "Trial job limit exhausted", status_code: int = 429):
        super().__init__(message=message, status_code=status_code)


class PlatformMetricsRecordAlreadyExistsError(BaseError):
    """Raised when a platform metrics record already exists."""

    def __init__(self, message: str = "Platform metrics record already exists", status_code: int = 409):
        super().__init__(message=message, status_code=status_code)


class PlatformMetricsUpdateError(BaseError):
    """Platform metrics failed to update error."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class PlatformMetricsAlreadyExistsUpdateError(BaseError):
    """Platform metrics failed to update error."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class TooManyAttemptsError(BaseError):
    """Raised when too many attempts are made."""

    def __init__(self, message: str = "Too many attempts", status_code: int = 429):
        super().__init__(message=message, status_code=status_code)


class HubSpotError(Exception):
    """Base exception for HubSpot API errors"""
    pass


class OperationNotSupportedError(BaseError):
    """Raised when an operation is not supported."""

    def __init__(self, message: str = "Too many attempts", status_code: int = 501):
        super().__init__(message=message, status_code=status_code)


class JobAlreadyRunningError(BaseError):
    """Raised when the job is already running."""

    def __init__(self, message: str = "Job is already running", status_code: int = 409):
        super().__init__(message=message, status_code=status_code)


class LatestTechSpecNotFoundError(BaseError):
    """Failed to find latest tech spec."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class GithubRepoAlreadyInSync(BaseError):
    """Raised when the github repo is already in sync."""

    def __init__(self, message: str = "Github repo is already in sync", status_code: int = 409):
        super().__init__(message=message, status_code=status_code)


class JobTypeNotSupportedError(BaseError):
    """Raised when the job type is not supported."""

    def __init__(self, message: str, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class CodeGenInformationNotFoundError(BaseError):
    """Raised when the code gen information is not found."""

    def __init__(self, message: str, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class ResourceNotFound(BaseError):
    """Raised when a resource is not found."""

    def __init__(self, message: str = "Resource not found", status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class AzureBaseError(BaseError):
    """Base class for Azure-related errors."""

    def __init__(self, message: str = "Azure operation failed", status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class OperationFailedError(BaseError):
    """Raised when an operation fails."""

    def __init__(self, message: str, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class DatabaseUpdateError(BaseError):
    """Database update error."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class GithubRepoAlreadyExistsError(BaseError):
    """Github repo already exists error."""

    def __init__(self, message: str = None, status_code: int = 409):
        super().__init__(message=message, status_code=status_code)


class GithubPermissionError(BaseError):
    """Github App lacks permission"""

    def __init__(self, message: str = None, status_code: int = 403):
        super().__init__(message=message, status_code=status_code)


class InvalidGithubRepoOnboardError(BaseError):
    """Invalid Github Repo onboarding error."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class Status404Error(BaseError):
    """Raised when the status code is 404."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class Status400Error(BaseError):
    """Raised when the status code is 400."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class PullRequestConflictError(BaseError):
    """Raised when attempting to merge the pull request"""

    def __init__(self, message: str = None, status_code: int = 409):
        super().__init__(message=message, status_code=status_code)


class PasswordDoesNotMeetRequirementsError(BaseError):
    """Raised when the password does not meet the requirements."""

    def __init__(self, message: str = None, status_code: int = 400, error_code="INVALID_PASSWORD"):
        super().__init__(message=message, status_code=status_code, error_code=error_code)


class BranchLockedError(BaseError):
    """Raised when the branch is locked."""

    def __init__(self, message: str = None, status_code: int = 409, error_code="BRANCH_LOCKED"):
        super().__init__(message=message, status_code=status_code, error_code=error_code)


class GithubBaseError(BaseError):
    """Raised when the Github API returns an error."""

    def __init__(self, message: str = None, status_code: int = 500, error_code="GITHUB_API_ERROR"):
        super().__init__(message=message, status_code=status_code, error_code=error_code)


class RevertTechSpecError(BaseError):
    """Raised when the revoking of tech spec fails."""

    def __init__(self, message: str = None, status_code: int = 409):
        super().__init__(message=message, status_code=status_code)


class EndpointMisconfigured(BaseError):
    """Raised when the endpoint is misconfigured(eg missing some env variables)"""

    def __init__(self, message: str = None, status_code: int = http.HTTPStatus.SERVICE_UNAVAILABLE):
        super().__init__(message=message, status_code=status_code)


class PRMergeError(BaseError):
    """Raised when the revoking of tech spec fails."""

    def __init__(self, message: str, status_code: int):
        super().__init__(message=message, status_code=status_code)
