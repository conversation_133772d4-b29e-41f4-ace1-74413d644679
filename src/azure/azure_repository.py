from typing import Dict, Any
from blitzy_utils.logger import logger

from src.error.errors import PRActionError, ResourceNotFound
from src.scm_base.base_classes import BaseRepository


class AzureRepository(BaseRepository):

    def __init__(
        self, azure_devops_connection, project_name, repo_id=None, repo_name=None
    ):
        """
        Initialize Azure DevOps repository instance.

        Args:
            azure_devops_connection: Azure DevOps connection instance
            project_name: Name of the Azure DevOps project
            repo_id: Repository ID (optional)
            repo_name: Repository name (optional)
        """
        self.connection = azure_devops_connection
        self.project_name = project_name
        self.repo_id = repo_id
        self.repo_name = repo_name

        logger.debug(
            f"Initialized AzureRepository for project {project_name}, repo {repo_name or repo_id}"
        )

    def create_branch(
        self, branch_name: str, base_branch: str = "main"
    ) -> Dict[str, Any]:
        """
        Create a new branch.

        :param branch_name: Name of the new branch
        :param base_branch: Base branch to create from
        :return: Dictionary with operation result
        """
        try:
            import requests

            logger.info(
                f"Creating branch '{branch_name}' from '{base_branch}' in repo {self.repo_name or self.repo_id}"
            )

            if not self.connection:
                raise Exception("Azure DevOps connection not available")

            # Get access token from connection
            access_token = self.connection.access_token
            if not access_token:
                raise Exception("Access token not available from connection")

            # First, get the base branch commit SHA
            base_branch_ref = f"refs/heads/{base_branch}"
            refs_url = f"https://dev.azure.com/{self.project_name}/_apis/git/repositories/{self.repo_id or self.repo_name}/refs?filter=heads/{base_branch}&api-version=6.0"

            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            }

            # Get base branch commit SHA
            refs_response = requests.get(refs_url, headers=headers, timeout=30)
            refs_response.raise_for_status()

            refs_data = refs_response.json()
            base_commit_sha = None

            for ref in refs_data.get("value", []):
                if ref.get("name") == base_branch_ref:
                    base_commit_sha = ref.get("objectId")
                    break

            if not base_commit_sha:
                raise Exception(f"Base branch '{base_branch}' not found")

            # Create new branch reference
            new_branch_ref = f"refs/heads/{branch_name}"
            create_ref_url = f"https://dev.azure.com/{self.project_name}/_apis/git/repositories/{self.repo_id or self.repo_name}/refs?api-version=6.0"

            ref_data = [
                {
                    "name": new_branch_ref,
                    "oldObjectId": "0000000000000000000000000000000000000000",
                    "newObjectId": base_commit_sha,
                }
            ]

            # Create the branch
            create_response = requests.post(
                create_ref_url, headers=headers, json=ref_data, timeout=30
            )
            create_response.raise_for_status()

            result_data = create_response.json()

            logger.info(
                f"Successfully created branch '{branch_name}' from '{base_branch}'"
            )

            return {
                "success": True,
                "branch_name": branch_name,
                "base_branch": base_branch,
                "commit_sha": base_commit_sha,
                "ref": new_branch_ref,
                "message": f"Branch '{branch_name}' created successfully",
            }

        except Exception as e:
            error_msg = f"Failed to create branch '{branch_name}': {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "branch_name": branch_name,
                "base_branch": base_branch,
            }

    def manage_pull_request(
        self, user_id: str, pr_id: int, action: str, merge_type: str = "merge"
    ):
        """
        Manages a pull request in a specified Azure DevOps repository. The method
        allows actions such as merging or abandoning a pull request. When merging,
        it checks if the pull request can be merged and attempts to do so using
        the specified merge type.

        Uses Azure DevOps credentials to authenticate and perform operations on
        a repository. Actions are performed based on the input parameters
        defining the operation and target pull request.

        :param user_id: ID of the user who initiated the operation.
        :param pr_id: ID of the pull request to manage.
        :param action: Specifies the action to perform on the pull request.
          Supported actions are "merge" or "abandon".
        :param merge_type: The type of merge to use for the pull request.
          Required if action is "merge". Possible values are "merge", "squash", or "rebase".
        :return: A dictionary containing the success status of the operation, an explanatory
          message, and optionally additional details (e.g., merge status, commit ID).
        :rtype: dict
        """
        try:
            import requests

            logger.info(
                f"Managing pull request {pr_id} with action '{action}' for user {user_id}"
            )

            # Validate action parameter
            valid_actions = ["merge", "abandon", "complete"]
            if action not in valid_actions:
                raise ValueError(
                    f"Invalid action '{action}'. Valid actions: {valid_actions}"
                )

            if not self.connection:
                raise Exception("Azure DevOps connection not available")

            # Get access token from connection
            access_token = self.connection.access_token
            if not access_token:
                raise Exception("Access token not available from connection")

            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            }

            # First, get pull request details to check current status
            pr_url = f"https://dev.azure.com/{self.project_name}/_apis/git/repositories/{self.repo_id or self.repo_name}/pullrequests/{pr_id}?api-version=6.0"

            pr_response = requests.get(pr_url, headers=headers, timeout=30)
            pr_response.raise_for_status()

            pr_data = pr_response.json()
            current_status = pr_data.get("status")

            logger.info(f"Pull request {pr_id} current status: {current_status}")

            # Prepare update payload based on action
            update_payload = {}

            if action == "merge" or action == "complete":
                # Check if PR can be merged
                if current_status != "active":
                    raise Exception(
                        f"Pull request is not active (status: {current_status})"
                    )

                # Map merge types to Azure DevOps merge strategy
                merge_strategy_map = {
                    "merge": "noFastForward",  # Creates merge commit
                    "squash": "squash",  # Squash commits
                    "rebase": "rebase",  # Rebase and merge
                }

                merge_strategy = merge_strategy_map.get(merge_type, "noFastForward")

                update_payload = {
                    "status": "completed",
                    "lastMergeSourceCommit": pr_data.get("lastMergeSourceCommit"),
                    "completionOptions": {
                        "mergeStrategy": merge_strategy,
                        "deleteSourceBranch": False,  # TODO: Make this configurable
                    },
                }

            elif action == "abandon":
                update_payload = {"status": "abandoned"}

            # Update the pull request
            update_response = requests.patch(
                pr_url, headers=headers, json=update_payload, timeout=30
            )
            update_response.raise_for_status()

            updated_pr = update_response.json()
            final_status = updated_pr.get("status")

            logger.info(
                f"Successfully {action}ed pull request {pr_id}. Final status: {final_status}"
            )

            result = {
                "success": True,
                "pr_id": pr_id,
                "action": action,
                "status": final_status,
                "message": f"Pull request {pr_id} {action}ed successfully",
            }

            # Add merge-specific details
            if action in ["merge", "complete"] and final_status == "completed":
                result.update(
                    {
                        "merge_type": merge_type,
                        "merge_commit_id": updated_pr.get("lastMergeCommit", {}).get(
                            "commitId"
                        ),
                        "source_branch": pr_data.get("sourceRefName"),
                        "target_branch": pr_data.get("targetRefName"),
                    }
                )

            return result

        except Exception as e:
            error_msg = f"Failed to {action} pull request {pr_id}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "pr_id": pr_id,
                "action": action,
            }
