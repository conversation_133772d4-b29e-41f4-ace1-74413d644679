# Azure DevOps Implementation Guide

## Overview

This document outlines the Azure DevOps integration that has been implemented to support both GitHub and Azure DevOps through a unified endpoint approach. The implementation reuses existing GitHub endpoints by checking the `svc_type` field in the `GithubInstallation` table to determine whether to route to GitHub or Azure DevOps handlers.

## Unified Endpoint Approach

Instead of maintaining separate Azure endpoints, the implementation now uses a **unified approach** where existing GitHub endpoints automatically detect the service type and route accordingly:

### 🔄 **Service Type Detection**
- Queries `GithubInstallation.svc_type` field for each user
- Routes to GitHub handlers when `svc_type = GITHUB`
- Routes to Azure DevOps handlers when `svc_type = AZURE_DEVOPS`

## Current Status

✅ **Implemented in archie-github-handler:**
- `POST /v1/azure/install` - Azure DevOps installation/authentication
- **Azure user endpoints have been removed** - Use unified GitHub endpoints instead

✅ **Unified Endpoints in archie-service-backend:**
- `GET /v1/github/accounts` - **UNIFIED:** Returns GitHub orgs OR Azure DevOps orgs based on user's `svc_type`
- `GET /v1/github/accounts/{account_name}/repositories` - **UNIFIED:** GitHub repos OR Azure repos (aggregated from all projects)
- `GET /v1/github/accounts/{account_name}/repositories/{repo_id}/branches` - **UNIFIED:** GitHub branches OR Azure branches (direct repository access)

⚠️ **Deprecated Azure Account Endpoint:**
- `GET /v1/azure/accounts` - **DEPRECATED** (use `/v1/github/accounts` instead)

✅ **Azure Project-Specific Endpoints in archie-service-backend:**
- `GET /v1/azure/accounts/{account_name}/projects` - Get projects in organization
- `GET /v1/azure/accounts/{account_name}/projects/{project_id}/repos` - Get repositories in project
- `GET /v1/azure/accounts/{account_name}/projects/{project_id}/repos/{repo_id}/branches` - Get branches in repository

## Unified Endpoint Implementation Details

### Service Type Detection Flow

```mermaid
graph TD
    A[User Request to /v1/github/accounts] --> B[Get Active Installation]
    B --> C{Check svc_type}
    C -->|GITHUB| D[Route to GitHub Handler]
    C -->|AZURE_DEVOPS| E[Route to Azure Handler]
    D --> F[Return GitHub Organizations]
    E --> G[Return Azure DevOps Organizations]
```

### Database Integration

The unified approach leverages the existing `GithubInstallation` table:

```sql
-- Example installation records
SELECT installation_id, svc_type, target_name, user_id
FROM github_installations
WHERE status = 'ACTIVE';

-- GitHub installation
installation_id: "12345"
svc_type: "GITHUB"
target_name: "my-github-org"

-- Azure DevOps installation
installation_id: "tenant-uuid"
svc_type: "AZURE_DEVOPS"
target_name: "my-azure-org"
```

## Azure DevOps Project-Based Architecture

Azure DevOps uses a hierarchical structure that differs from GitHub:

```
Organization
├── Project A
│   ├── Repository 1
│   ├── Repository 2
│   └── Repository 3
└── Project B
    ├── Repository 4
    └── Repository 5
```

This requires project-aware endpoints to properly navigate the Azure DevOps structure.

## Azure DevOps Handler Implementation

**Note:** Azure user endpoints (`/v1/azure/users/*`) have been **removed** from archie-github-handler. Azure DevOps functionality is now accessed through unified GitHub endpoints that automatically detect service type.

### Unified Endpoint Routing

The archie-github-handler service now routes Azure DevOps requests through the unified GitHub user endpoints:
- `GET /v1/github/users/{user_id}/organizations` - Handles both GitHub and Azure DevOps organizations
- `GET /v1/github/users/{user_id}/organizations/{org_name}/projects` - Azure DevOps projects (when service type is AZURE_DEVOPS)
- `GET /v1/github/users/{user_id}/organizations/{org_name}/projects/{project_id}/repositories` - Azure DevOps repositories
- `GET /v1/github/users/{user_id}/organizations/{org_name}/projects/{project_id}/repositories/{repo_id}/branches` - Azure DevOps branches
- `GET /v1/github/users/{user_id}/organizations/{org_name}/repositories/{repo_id}/branches` - Direct repository branch access

**Note:** For Azure DevOps endpoints, the `{org_name}` parameter in the URL path is treated as `org_id` (Azure DevOps organization ID) internally by the service functions.

## Unified Endpoint Behavior

### GET /v1/github/accounts
**Unified behavior based on user's installation type:**

| User's svc_type | Behavior | Response |
|----------------|----------|----------|
| `GITHUB` | Calls GitHub handler | Returns GitHub organizations |
| `AZURE_DEVOPS` | Calls Azure handler | Returns Azure DevOps organizations |

**Implementation:**
```python
installation = get_active_github_installation_by_user_id(user_id)
if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
    # Calls unified GitHub endpoint which routes to Azure handler
    response = client.get("github", f"/v1/github/users/{user_id}/organizations")
else:
    response = get_organizations_from_github_handler(user_id)
```

### GET /v1/github/accounts/{account_name}/repositories
**Unified behavior - works for both service types:**

| User's svc_type | Behavior | Response |
|----------------|----------|----------|
| `GITHUB` | Returns repositories | GitHub repositories from organization |
| `AZURE_DEVOPS` | Aggregates from all projects | Azure DevOps repositories from all projects in organization |

**Implementation for Azure DevOps:**
- Gets all projects in the organization
- Fetches repositories from each project
- Aggregates and returns all repositories with project context

### GET /v1/github/accounts/{account_name}/repositories/{repo_id}/branches
**Unified behavior - works for both service types:**

| User's svc_type | Behavior | Response |
|----------------|----------|----------|
| `GITHUB` | Returns branches | GitHub branches from repository |
| `AZURE_DEVOPS` | Direct repository access | Azure branches from repository (no project lookup needed) |

**Implementation for Azure DevOps:**
- Calls Azure DevOps API directly using repository ID
- No project name lookup required (Azure API supports direct repo access)
- Returns branches from the specific repository efficiently

## Azure-Specific Endpoints

### ⚠️ Deprecated Account Endpoint

### 1. GET /v1/azure/accounts (DEPRECATED)
- **Status:** **DEPRECATED** - Returns HTTP 410 Gone
- **Replacement:** Use `GET /v1/github/accounts` instead
- **Reason:** Unified approach eliminates need for separate Azure account endpoint

### ✅ Active Project-Based Endpoints

For Azure DevOps project-based operations, these dedicated endpoints remain active:

### 2. GET /v1/azure/accounts/{account_name}/projects
- **Purpose:** Get projects in an Azure DevOps organization
- **Calls:** `GET /v1/github/users/{user_id}/organizations/{account_name}/projects` in archie-github-handler (unified endpoint)

### 3. GET /v1/azure/accounts/{account_name}/projects/{project_id}/repos
- **Purpose:** Get repositories in an Azure DevOps project
- **Calls:** `GET /v1/github/users/{user_id}/organizations/{account_name}/projects/{project_id}/repositories` in archie-github-handler (unified endpoint)

### 4. GET /v1/azure/accounts/{account_name}/repos/{repo_id}/branches
- **Purpose:** Get branches in an Azure DevOps repository
- **Calls:** `GET /v1/github/users/{user_id}/organizations/{account_name}/repositories/{repo_id}/branches` in archie-github-handler (unified endpoint)
- **Note:** The project_id is derived automatically from the GitHubProjectRepo table using repo_id and account_name

## Implementation Details

### Authentication
- Uses existing Azure token management from `/v1/azure/install`
- Leverages `fetch_azure_secret_for_user()` function for token retrieval
- Handles token refresh automatically through existing infrastructure

### Error Handling
- Follows same error handling patterns as GitHub endpoints
- Uses `AzureBaseError` and `ResourceNotFound` exceptions
- Comprehensive logging for debugging and monitoring

### Response Format
- Matches GitHub endpoint response structures for consistency
- Uses Pydantic models for type safety and validation
- Consistent error response format across all endpoints

## Entity Mapping

| GitHub | Azure DevOps | Notes |
|--------|--------------|-------|
| Organization | Organization | Top-level container |
| N/A | Project | Azure DevOps specific organizational layer |
| Repository | Repository | Code repositories within projects |
| Branch | Git Ref | Branch references (refs/heads/*) |
| Commit | Commit | Individual commits |
| PR | Pull Request | Code review requests |
| Installation ID | Tenant ID | Authentication scope identifier |

## Key Differences from GitHub

1. **Project Layer:** Azure DevOps has an additional project layer between organizations and repositories
2. **API Structure:** Requires project context for most repository operations
3. **Authentication:** Uses Azure AD tokens instead of GitHub tokens
4. **Hierarchy:** Organization → Project → Repository vs GitHub's Organization → Repository

## Testing

The implementation has been tested for:

1. ✅ **Syntax Validation:** All Python files compile successfully
2. ✅ **Import Validation:** All modules and functions import correctly
3. ✅ **Model Validation:** Pydantic models validate properly
4. ✅ **Route Registration:** All endpoints register correctly

### Manual Testing Checklist

To test the endpoints manually:

1. **Authentication:** Ensure Azure authentication works via `/v1/azure/install`
2. **Organizations:** Test `GET /v1/github/accounts` with valid Azure DevOps credentials (unified endpoint)
3. **Projects:** Test `GET /v1/azure/accounts/{account_name}/projects`
4. **Repositories:** Test `GET /v1/azure/accounts/{account_name}/projects/{project_id}/repos`
5. **Branches:** Test `GET /v1/azure/accounts/{account_name}/projects/{project_id}/repos/{repo_id}/branches`
6. **Error Scenarios:** Test with invalid org, project, repo, or no access

## Implementation Status

✅ **COMPLETED:**
1. **Unified Endpoint Approach:** GitHub endpoints now support both GitHub and Azure DevOps
2. **Service Type Detection:** Automatic routing based on `GithubInstallation.svc_type`
3. **Azure Handler Integration:** Azure DevOps functionality integrated into unified GitHub endpoints
4. **Database Integration:** Project lookup functionality for Azure DevOps repositories
5. **Graceful Degradation:** Deprecation notices for Azure users on organization-level endpoints
6. **Backward Compatibility:** Existing GitHub integrations continue to work unchanged
7. **Error Handling and Logging:** Comprehensive error handling across all endpoints
8. **Authentication Integration:** Unified token management for both GitHub and Azure DevOps
9. **Azure User Endpoint Removal:** Removed separate Azure user endpoints in favor of unified approach

## API Usage Examples

### Get Organizations (Updated)
```bash
# Use unified GitHub endpoint instead
GET /v1/github/accounts
Authorization: Bearer {user_token}
```

### Get Projects in Organization
```bash
GET /v1/azure/accounts/myorg/projects
Authorization: Bearer {user_token}
```

### Get Repositories in Project
```bash
GET /v1/azure/accounts/myorg/projects/myproject/repos
Authorization: Bearer {user_token}
```

### Get Branches in Repository
```bash
GET /v1/azure/accounts/myorg/projects/myproject/repos/repo-id/branches
Authorization: Bearer {user_token}
```

## References

- [Azure DevOps REST API Documentation](https://docs.microsoft.com/en-us/rest/api/azure/devops/)
- [Azure DevOps Git API](https://docs.microsoft.com/en-us/rest/api/azure/devops/git/)
- [Azure DevOps Core API](https://docs.microsoft.com/en-us/rest/api/azure/devops/core/)
- [Azure DevOps Projects API](https://docs.microsoft.com/en-us/rest/api/azure/devops/core/projects/)

## Benefits of Unified Approach

### 🔄 **Code Reuse**
- Single set of endpoints handles both GitHub and Azure DevOps
- Reduces maintenance overhead and code duplication
- Consistent API experience regardless of underlying VCS

### 🔧 **Backward Compatibility**
- Existing GitHub integrations continue to work unchanged
- No breaking changes for current users
- Smooth migration path for mixed environments

### 📊 **Database-Driven Routing**
- Leverages existing `GithubInstallation` table structure
- Automatic service detection based on user's installation
- No need for separate authentication flows

### 🎯 **Clear Migration Guidance**
- Azure DevOps users get helpful deprecation notices
- Clear direction toward project-based endpoints
- Maintains functionality while encouraging best practices

## Usage Examples

### For GitHub Users (No Change)
```bash
# Works exactly as before
GET /v1/github/accounts                                    # GitHub organizations
GET /v1/github/accounts/my-org/repositories               # GitHub repositories
GET /v1/github/accounts/my-org/repositories/123/branches  # GitHub branches
```

### For Azure DevOps Users (Fully Unified)
```bash
# ✅ UNIFIED ENDPOINTS: All work seamlessly with Azure DevOps
GET /v1/github/accounts                                    # Returns Azure DevOps organizations
GET /v1/github/accounts/my-org/repositories               # Returns repos from ALL projects in org
GET /v1/github/accounts/my-org/repositories/repo-id/branches # Returns Azure branches (with project lookup)

# ⚠️ DEPRECATED: Do not use
# GET /v1/azure/accounts  # Returns HTTP 410 Gone

# ✅ OPTIONAL: Project-based endpoints for specific project operations
GET /v1/azure/accounts/my-org/projects                           # Get projects in organization
GET /v1/azure/accounts/my-org/projects/my-project/repos          # Get repos in specific project
GET /v1/azure/accounts/my-org/projects/my-project/repos/repo-id/branches # Get branches in specific project
```

## Conclusion

The Azure DevOps integration now provides a unified approach that:
- **Reuses existing GitHub endpoints** through intelligent service type detection
- **Maintains full Azure DevOps project-based functionality** through dedicated endpoints
- **Ensures backward compatibility** for all existing GitHub integrations
- **Provides clear migration paths** for Azure DevOps users

This implementation successfully bridges the gap between GitHub's organization-based structure and Azure DevOps' project-based hierarchy while maintaining a consistent and intuitive API experience.
