openapi: 3.0.0
info:
  title: Blitzy Github Handler
  version: 1.0.1
  description: API for managing github secrets

servers:
- url: /v1
  description: Version 1 of the API

paths:
  /github/project/{projectId}:
    get:
      summary: Get GitHub project details
      description: Retrieves the details of a specific GitHub project
      parameters:
      - in: path
        name: projectId
        required: true
        schema:
          type: string
        description: Unique identifier of the project
      responses:
        '200':
          description: Successfully retrieved project details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GitHubProjectOutput'
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/installations:
    get:
      summary: Get GitHub installations for user
      description: Retrieves all GitHub installations associated with a specific user
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      responses:
        '200':
          description: Successfully retrieved installations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetGithubInstallationOutputArray'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '409':
          description: Organization already has active AZURE integration
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status409'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations:
    get:
      summary: Get GitHub organizations for user
      description: Retrieves all GitHub organizations associated with a specific user
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      responses:
        '200':
          description: Successfully retrieved organizations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GithubOrgBasicList'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations/<org_name>/repositories:
    get:
      summary: Get GitHub repositories for user
      description: Retrieves all GitHub repositories associated with a specific user, optionally filtered by organization
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: org_name
        required: true
        schema:
          type: string
        description: Filter repositories by organization name
      responses:
        '200':
          description: Successfully retrieved repositories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RepositoryList'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations/<org_name>/repositories/{repoId}/branches:
    get:
      summary: Get branches for a specific repository
      description: Retrieves all branches for a specific repository within a user's installation
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: installationId
        required: true
        schema:
          type: string
        description: Unique identifier of the GitHub installation
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      responses:
        '200':
          description: Successfully retrieved branches
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BranchList'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations/<org_name>/repositories/{repoId}/default/branch:
    get:
      summary: Get default branch for a specific repository
      description: Retrieves default branch for a specific repository within a user's installation
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: installationId
        required: true
        schema:
          type: string
        description: Unique identifier of the GitHub installation
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      responses:
        '200':
          description: Successfully retrieved branches
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DefaultBranchOutput'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'


  /github/users/{userId}/organizations/<org_name>/repositories/{repoId}/branch/{branchName}/head/commit:
    get:
      summary: Get latest branch commit
      description: Retrieves latest branch commit details from particular branch
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: installationId
        required: true
        schema:
          type: string
        description: Unique identifier of the GitHub installation
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      - in: path
        name: branchName
        required: true
        schema:
          type: string
        description: Unique identifier of the branch
      responses:
        '200':
          description: Successfully retrieved branches
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BranchCommitHistoryCommit'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'


  /github/secret:
    post:
      summary: Create a new secret
      description: Creates a new GitHub token secret for a user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SecretInput'
      responses:
        '201':
          description: Secret created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretsOutput'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /azure/install:
    post:
      summary: Creates and configure a new azure devops "installation"
      description: Called for new devops installation to create proper secrets and RDS records
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AzureInstallation'
      responses:
        '201':
          description: Secret created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretsOutput'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '503':
          description: Service unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status503'

  /v1/azure/users/{userId}/organizations:
    get:
      summary: Get Azure DevOps organizations for user
      description: Retrieves all Azure DevOps organizations associated with a specific user
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      responses:
        '200':
          description: Successfully retrieved organizations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AzureOrgBasicList'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /v1/azure/users/{userId}/organizations/{orgId}/projects:
    get:
      summary: Get Azure DevOps projects for user and organization
      description: Retrieves all projects for a specific user and organization in Azure DevOps
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgId
        required: true
        schema:
          type: string
        description: Azure DevOps organization ID (GUID)
      responses:
        '200':
          description: Successfully retrieved projects
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AzureProjectList'
        '404':
          description: Organization not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /v1/azure/users/{userId}/organizations/{orgId}/projects/{projectId}/repositories:
    get:
      summary: Get Azure DevOps repositories for user, organization, and project
      description: Retrieves all repositories for a specific user, organization, and project in Azure DevOps
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgId
        required: true
        schema:
          type: string
        description: Azure DevOps organization ID (GUID)
      - in: path
        name: projectId
        required: true
        schema:
          type: string
        description: Azure DevOps project ID
      responses:
        '200':
          description: Successfully retrieved repositories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RepositoryList'
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /v1/azure/users/{userId}/organizations/{orgId}/repositories/{repoId}/branches:
    get:
      summary: Get Azure DevOps branches for repository (project auto-detected)
      description: Retrieves all branches for a specific repository with automatic project detection
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgId
        required: true
        schema:
          type: string
        description: Azure DevOps organization ID (GUID)
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Azure DevOps repository ID
      responses:
        '200':
          description: Successfully retrieved branches
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BranchList'
        '404':
          description: Repository not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /v1/azure/users/{userId}/organizations/{orgId}/projects/{projectId}/repositories/{repoId}/branches:
    get:
      summary: Get Azure DevOps branches for repository with explicit project
      description: Retrieves all branches for a specific repository within a specific project
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgId
        required: true
        schema:
          type: string
        description: Azure DevOps organization ID (GUID)
      - in: path
        name: projectId
        required: true
        schema:
          type: string
        description: Azure DevOps project ID
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Azure DevOps repository ID
      responses:
        '200':
          description: Successfully retrieved branches
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BranchList'
        '404':
          description: Repository not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /secret/{installation_id}:
    get:
      summary: Get user's GitHub token
      description: Retrieves the GitHub token for a specific github installation
      parameters:
      - in: path
        name: installationId
        required: true
        schema:
          type: string
        description: Unique identifier of the github installation
      - in: query
        name: version
        required: false
        schema:
          type: string
        description: Version of the secret to retrieve
        example: latest
      responses:
        '200':
          description: Successfully retrieved GitHub token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretsOutput'
        '404':
          description: Installation not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/repositories/{repoId}/installation:
    get:
      summary: Get GitHub installation for a repository
      description: Retrieves the GitHub installation associated with a specific repository
      parameters:
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      responses:
        '200':
          description: Successfully retrieved installation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetGithubInstallationOutput'
        '404':
          description: Repository or installation not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/repositories/{repoId}/secret:
    get:
      summary: Get GitHub installation for a repository
      description: Retrieves the GitHub installation associated with a specific repository
      parameters:
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      responses:
        '200':
          description: Successfully retrieved installation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretsOutput'
        '404':
          description: Repository or installation not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/operations/repositories/{repoId}/branches:
    post:
      summary: Create a new branch
      description: Creates a new branch in a GitHub repository
      parameters:
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBranchInput'
      responses:
        '201':
          description: Branch created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Branch'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '404':
          description: Repository not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/operations/repositories/{repoId}/branches/{branchName}/commits:
    post:
      summary: Create a new commit
      description: Creates a new commit with changes to one or more files in a specific branch
      parameters:
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      - in: path
        name: branchName
        required: true
        schema:
          type: string
        description: Name of the branch to commit to
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCommitInput'
      responses:
        '201':
          description: Commit created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommitOutput'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '404':
          description: Repository or branch not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/operations/repositories/{repo_id}/pr:
    post:
      summary: Create a new pull request
      description: Creates a new pull request in a GitHub repository
      parameters:
      - in: path
        name: repo_id
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePullRequestInput'
      responses:
        '201':
          description: Pull request created successfully
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '404':
          description: Repository or branches not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations/<orgName>/repositories/{repoId}/pr/{prNumber}/action:
    post:
      summary: Create a new pull request
      description: Creates a new pull request in a GitHub repository
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgName
        required: true
        schema:
          type: string
        description: Name of the organization
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      - in: path
        name: prNumber
        required: true
        schema:
          type: string
        description: Unique identifier of the PR
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PRActionInput'
      responses:
        '201':
          description: Pull request processed successfully
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '404':
          description: Repository or branches not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations/<orgName>/repositories/{repoId}/pr/{prNumber}/status:
    get:
      summary: Get PR status from GitHub
      description: Retrieves the current status of a specific pull request directly from GitHub API
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgName
        required: true
        schema:
          type: string
        description: Name of the organization
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      - in: path
        name: prNumber
        required: true
        schema:
          type: string
        description: Pull request number
      responses:
        '200':
          description: Successfully retrieved PR status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PRStatusOutput'
        '404':
          description: PR not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/operations/organizations/{orgName}/repositories:
    post:
      summary: Create a new repository in an organization
      description: Creates a new GitHub repository within a specified organization
      parameters:
      - in: path
        name: orgName
        required: true
        schema:
          type: string
        description: Name of the organization
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRepositoryInput'
      responses:
        '201':
          description: Repository created successfully
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '404':
          description: Organization not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/secret/user:
    post:
      summary: Create a new secret
      description: Creates a new GitHub token secret for a user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserSecretInput'
      responses:
        '201':
          description: Secret created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretsOutput'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/secret/user/{userId}:
    get:
      summary: Get user's GitHub token
      description: Retrieves the GitHub token for a specific user
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: query
        name: version
        required: false
        schema:
          type: string
        description: Version of the secret to retrieve
        example: latest
      responses:
        '200':
          description: Successfully retrieved GitHub token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretsOutput'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations/{orgName}/repositories/{repoId}:
    get:
      summary: Get repository details
      description: Retrieves details of a specific repository within an organization for a particular user
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgName
        required: true
        schema:
          type: string
        description: Name of the organization
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      responses:
        '200':
          description: Successfully retrieved repository details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Repository'
        '404':
          description: Repository, organization or user not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations/{orgName}/projects:
    get:
      summary: Get projects for user and organization
      description: Retrieves all projects for a specific user and organization (Azure DevOps only)
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgName
        required: true
        schema:
          type: string
        description: Name of the organization
      responses:
        '200':
          description: Successfully retrieved projects
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AzureProjectList'
        '404':
          description: Organization not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations/{orgName}/projects/{projectId}/repositories:
    get:
      summary: Get repositories for user, organization, and project
      description: Retrieves all repositories for a specific user, organization, and project (Azure DevOps only)
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgName
        required: true
        schema:
          type: string
        description: Name of the organization
      - in: path
        name: projectId
        required: true
        schema:
          type: string
        description: Unique identifier of the project
      responses:
        '200':
          description: Successfully retrieved repositories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RepositoryList'
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/users/{userId}/organizations/{orgName}/projects/{projectId}/repositories/{repoId}/branches:
    get:
      summary: Get branches for user, organization, project, and repository
      description: Retrieves all branches for a specific user, organization, project, and repository (Azure DevOps only)
      parameters:
      - in: path
        name: userId
        required: true
        schema:
          type: string
        description: Unique identifier of the user
      - in: path
        name: orgName
        required: true
        schema:
          type: string
        description: Azure DevOps organization ID (GUID) - treated as org_id internally
      - in: path
        name: projectId
        required: true
        schema:
          type: string
        description: Unique identifier of the project
      - in: path
        name: repoId
        required: true
        schema:
          type: string
        description: Unique identifier of the repository
      responses:
        '200':
          description: Successfully retrieved branches
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BranchList'
        '404':
          description: Repository not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /github/repositories/repos/{repo_id}/access-token:
    get:
      summary: Retrieve SCM access token
      description: >
        Retrieves the SCM (e.g., GitHub or Azure DevOps) access token for the specified repository.
        Determines the repository's SCM provider and delegates token-fetching logic accordingly.

        Supported SCMs:
        - GitHub
        - Azure DevOps
      operationId: getRepositoryAccessToken
      tags:
      - Repositories
      parameters:
      - name: repo_id
        in: path
        required: true
        description: The unique identifier of the repository.
        schema:
          type: string
      responses:
        '200':
          description: Successfully retrieved SCM access token.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretsOutput'
        '400':
          description: Unsupported or improperly configured SCM type.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Access token has expired.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'
        '404':
          description: Repository or token not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '502':
          description: Failed to refresh access token from SCM provider.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status502'


components:
  schemas:
    Status200:
      type: object
      properties:
        message:
          type: string
          example: Successfully retrieved GitHub token

    Status201:
      type: object
      properties:
        message:
          type: string
          example: Successfully created github repo

    Status400:
      type: object
      properties:
        message:
          type: string
          example: Invalid request parameters

    Status404:
      type: object
      properties:
        message:
          type: string
          example: User not found

    Status401:
      type: object
      properties:
        message:
          type: string
          example: Unauthorized access

    Status409:
      type: object
      properties:
        message:
          type: string
          example: Something went wrong

    Status500:
      type: object
      properties:
        message:
          type: string
          example: Something went wrong

    Status502:
      type: object
      properties:
        message:
          type: string
          example: Bad Gateway

    Status503:
      type: object
      properties:
        message:
          type: string
          example: Something went wrong

    GitHubProjectOutput:
      type: object
      properties:
        repoId:
          type: string
          description: Repository ID
          maxLength: 100
        repoName:
          type: string
          description: Repository name
          maxLength: 255
        repoFullName:
          type: string
          description: Full repository name
          maxLength: 255
        status:
          type: string
          description: GitHub repository status

    GetGithubInstallationOutputArray:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/GetGithubInstallationOutput'


    GetGithubInstallationOutput:
      type: object
      properties:
        status:
          type: string
          description: Current status of the installation
        requiresApproval:
          type: boolean
          description: Whether installation requires approval
        createdAt:
          type: integer
          description: Timestamp when the record was created
          example: **********
        updatedAt:
          type: integer
          description: Timestamp when the record was last updated
          example: **********
        installation_id:
          type: string
          description: Github Installation ID
          example: ********
        installation_type:
          type: string
          description: Type of Github Installation
          example: USER
        target_name:
          type: string
          description: Account name
          example: account-name
        svc_type:
          type: string
          description: Version control system user has integration with
          example: GITHUB

    GithubOrgBasicList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/GithubOrgBasic'

    GithubOrgBasic:
      type: object
      properties:
        id:
          type: string
          description: ID of the GitHub account
          example: '12345'
        name:
          type: string
          description: Name of the GitHub account
          example: microsoft
        type:
          type: string
          description: Type of the account
          example: Organization
        installationId:
          type: string
          description: Installation ID used to fetch this org
          example: '67890'

    Repository:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier of the repository
          example: '********'
        name:
          type: string
          description: Name of the repository
          example: repo-name
        fullName:
          type: string
          description: Full name of the repository including owner
          example: microsoft/repo-name
        private:
          type: boolean
          description: Whether the repository is private
          example: true
        installationId:
          type: string
          description: Installation ID associated with this repository
          example: '********'

    RepositoryList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/Repository'
      required:
      - results

    Branch:
      type: object
      properties:
        name:
          type: string
          description: Name of the branch
          example: main
        protected:
          type: boolean
          description: Whether the branch is protected
          example: true

    BranchList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/Branch'

    AzureOrgBasic:
      type: object
      properties:
        id:
          type: string
          description: Azure DevOps organization ID
          example: 'org-12345'
        name:
          type: string
          description: Azure DevOps organization name
          example: 'my-organization'
        url:
          type: string
          description: Azure DevOps organization URL
          example: 'https://dev.azure.com/my-organization'

    AzureOrgBasicList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/AzureOrgBasic'

    AzureProject:
      type: object
      properties:
        id:
          type: string
          description: Azure DevOps project ID
          example: 'project-12345'
        name:
          type: string
          description: Azure DevOps project name
          example: 'my-project'
        description:
          type: string
          description: Azure DevOps project description
          example: 'My Azure DevOps project'
        url:
          type: string
          description: Azure DevOps project URL
          example: 'https://dev.azure.com/my-organization/my-project'

    AzureProjectList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/AzureProject'

    DefaultBranchOutput:
      type: object
      properties:
        branch:
          type: string
          description: Default branch name

    BranchCommitHistoryCommit:
      type: object
      additionalProperties: true

    SecretInput:
      type: object
      properties:
        accessToken:
          type: string
          description: access token from GitHub
        code:
          type: string
          description: GitHub returned oauth code for the user
        installationID:
          type: string
          description: GitHub installation ID of the user
        setupAction:
          type: string
          description: Setup action
      required:
      - installationID
      - setupAction
      - code

    AzureInstallation:
      type: object
      properties:
        user_id:
          type: string
          description: Our internal user id(id from users table)
        code:
          type: string
          description: Azure returned oauth code for the user
        redirect_uri:
          type: string
          description: Redirect URI we used during oauth, required for token exchange and refresh token usage
        requested_scope:
          type: string
          description: Scope our azure app requested, required for refresh token usage
      required:
      - code
      - user_id
      - redirect_uri
      - requested_scope


    SecretsOutput:
      type: object
      properties:
        accessToken:
          type: string
          description: access token from GitHub
        code:
          type: string
          description: GitHub returned oauth code for the user
        installationID:
          type: string
          description: GitHub installation ID of the user
        setupAction:
          type: string
          description: Setup action
        scvType:
          type: string
          description: SCV Type

    CreateBranchInput:
      type: object
      properties:
        branchName:
          type: string
          description: Name of the new branch to create
          example: feature/new-feature
        baseBranch:
          type: string
          description: Name of the branch to use as the base
          example: main
        description:
          type: string
          description: Description of the new branch
          example: Branch for implementing new feature XYZ
      required:
      - branchName
      - baseBranch

    FileOperation:
      type: string
      enum:
      - CREATE
      - UPDATE
      - DELETE
      description: Type of operation to perform on the file

    CommitFileInput:
      type: object
      properties:
        path:
          type: string
          description: Path to the file in the repository
          example: src/main.py
        content:
          type: string
          description: Content of the file
          example: "def hello():\n return 'world'"
        operation:
          $ref: '#/components/schemas/FileOperation'
          description: Type of operation to perform
          example: CREATE
      required:
      - path
      - operation

    CreateCommitInput:
      type: object
      properties:
        message:
          type: string
          description: Commit message
          example: Add implementation for XYZ feature
        files:
          type: array
          description: Files to include in the commit
          items:
            $ref: '#/components/schemas/CommitFileInput'
      required:
      - installationID
      - message
      - files

    CommitInfo:
      type: object
      properties:
        sha:
          type: string
          description: The SHA hash of the commit
          example: def456...
        url:
          type: string
          description: URL to the commit on GitHub
          example: https://github.com/acme-corp/project-x/commit/def456...
        message:
          type: string
          description: Commit message
          example: Add implementation for XYZ feature
      required:
      - sha
      - url
      - message

    BranchRef:
      type: object
      properties:
        name:
          type: string
          description: Name of the branch
          example: feature/new-feature
        sha:
          type: string
          description: The SHA hash of the branch's HEAD
          example: def456...
      required:
      - name
      - sha

    CommitFileOutput:
      type: object
      properties:
        path:
          type: string
          description: Path to the file in the repository
          example: src/main.py
        status:
          type: string
          description: Status of the file after the commit
          enum: [added, modified, removed]
          example: added
      required:
      - path
      - status

    CommitOutput:
      type: object
      required:
      - commit
      - branch
      - files
      properties:
        commit:
          $ref: '#/components/schemas/CommitInfo'
        branch:
          $ref: '#/components/schemas/BranchRef'
        files:
          type: array
          description: Information about the files modified in the commit
          items:
            $ref: '#/components/schemas/CommitFileOutput'

    CreatePullRequestInput:
      type: object
      properties:
        title:
          type: string
          description: Title of the pull request
          example: Add new feature XYZ
        description:
          type: string
          description: Detailed description of the pull request
          example: This PR implements feature XYZ which allows users to...
        headBranch:
          type: string
          description: Name of the branch containing the changes
          example: feature/new-feature
        baseBranch:
          type: string
          description: Name of the branch to merge changes into
          example: main
          default: main
        draft:
          type: boolean
          description: Whether to create the pull request as a draft
          default: false
        maintainerCanModify:
          type: boolean
          description: Whether maintainers can modify the pull request
          example: true
          default: true
      required:
      - title
      - headBranch
      - baseBranch

    CreateRepositoryInput:
      type: object
      properties:
        repoName:
          type: string
          description: Name of the new repository
          example: new-project
        private:
          type: boolean
          description: Whether the repository should be private
          default: true
        userId:
          type: string
          description: ID of the user creating the repository
          example: user-123
        azureProjectId:
          type: string
          description: ID of the azure project
          example: '123'
        azureOrgId:
          type: string
          description: ID of the azure org
          example: '123'
      required:
      - name
      - private
      - userId

    UserSecretInput:
      type: object
      properties:
        userId:
          type: string
          description: Unique identifier of the user
        accessToken:
          type: string
          description: access token from GitHub
        code:
          type: string
          description: GitHub returned oauth code for the user
        installationID:
          type: string
          description: GitHub installation ID of the user
        setupAction:
          type: string
          description: Setup action
      required:
      - userId
      - installationID
      - setupAction
      - code

    PRActionInput:
      type: object
      required:
      - action
      properties:
        action:
          type: string
          description: >
            Action to be performed on the pull request.
            Use "MERGE" to merge the PR or "CLOSE" to close it without merging.
          enum: [MERGE, CLOSE]
          example: MERGE

    PRStatusOutput:
      type: object
      properties:
        status:
          type: string
          description: PR status
          enum: [PENDING, MERGED, REJECTED, CLOSED, DONE]
          example: MERGED
