swagger: "2.0"
info:
  version: 0.0.65
  title: Blitzy OS Dev API
  contact:
    email: <EMAIL>
host: os.api.blitzy.dev
basePath: /v1
schemes:
  - https
produces:
  - application/json
tags:
  - name: documents
    description: Manage documents.
  - name: auth
    description: Manage authentication.
  - name: jobs
    description: Manage jobs.
x-google-endpoints:
  - name: blitzy-os-dev-1a9sfle4zdmnu.apigateway.blitzy-os-dev.cloud.goog
    allowCors: true
x-google-backend:
  address: https://archie-service-backend-************.us-central1.run.app
  path_translation: APPEND_PATH_TO_ADDRESS
  deadline: 300.0
paths:
  /auth:
    post:
      tags:
        - auth
      summary: Exchange authentication tokens.
      description: ""
      operationId: exchangeAuthTokens
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: header
          name: X-ID-Token
          description: ID token provided by Authentication.
          type: string
          required: false
        - in: header
          name: X-Basic-Auth
          description: HTTPS Basic auth with base64 username:password.
          type: string
          required: false
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Tokens"
        "401":
          description: "Unauthorized: Invalid Input"

  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user with email and password
      operationId: loginUser
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Login credentials
          required: true
          schema:
            $ref: "#/definitions/LoginInput"
      responses:
        "200":
          description: Login successful
          schema:
            $ref: "#/definitions/LoginOutput"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "401":
          description: Invalid credentials
          schema:
            $ref: "#/definitions/Status401"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user/register:
    post:
      tags:
        - Authentication
      summary: Register new user
      description: Register a new user with email and password
      operationId: registerUser
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Registration credentials
          required: true
          schema:
            $ref: "#/definitions/RegisterUserInput"
      responses:
        "200":
          description: Registration successful
          schema:
            $ref: "#/definitions/Status201"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "409":
          description: User already exists
          schema:
            $ref: "#/definitions/Status409"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /stripe/webhook:
    post:
      tags:
        - stripe
      summary: Stripe webhook
      description: ""
      operationId: stripWebhook
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Status200"
        "401":
          description: "Unauthorized: Invalid Input"

  /azure/auth_url:
    get:
      tags:
        - Azure
      summary: Return URL user has to be redirected for authentication
      operationId: GetAzureAuthURL
      security:
        - jwt_auth: []
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetAzureAuthURL"
        "503":
          description: Azure service misconfigured on a server
          schema:
            $ref: "#/definitions/Status503"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /azure/auth/success:
    get:
      tags:
        - Azure
      summary: A URL Azure cloud will send user to after successful authentication
      operationId: getAzureCallback
      produces:
        - application/json
      parameters:
        - name: code
          in: path
          description: Code we will use in exchange to get auth token
          required: true
          type: string
        - name: state
          in: path
          description: Random cryptographic string to protect from CSRF
          required: true
          type: string

      responses:
        "200":
          description: Successful operation
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /git:
    get:
      tags:
        - GitHub
        - Azure
      summary: Get git(azure and devops now) integrations details
      description: |
        Retrieves information about the user's active git integrations,
        including GitHub and Azure DevOps. Returns a list of active integrations.
        If the user has not connected their GitHub or Azure DevOps account,
        those respective records will not be present in the response list.
      operationId: getGitInstallations
      produces:
        - application/json
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetGitInstallationsOutput"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /github:
    get:
      tags:
        - GitHub
      summary: Get GitHub installation details
      description: Retrieve GitHub app installation information
      operationId: getGithubInstallation
      produces:
        - application/json
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetGithubInstallationList"
        "400":
          description: Invalid request
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Installation not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /github/accounts:
    get:
      tags:
        - GitHub
      summary: List GitHub accounts
      description: Retrieve list of GitHub organizations/users for the authenticated user
      operationId: listGithubAccounts
      produces:
        - application/json
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GithubAccountBasicList"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /github/accounts/{account_name}/repositories:
    get:
      tags:
        - GitHub
        - Repositories
      summary: List repositories of a GitHub account
      description: Retrieve list of repositories for a specific GitHub organization/user
      operationId: listRepositories
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: account_name
          in: path
          description: Name of the GitHub organization/user
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/RepositoryList"
        "404":
          description: Account not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /github/accounts/{account_name}/repositories/{repo_id}/branches:
    get:
      tags:
        - GitHub
        - Repositories
        - Branches
      summary: List branches of a GitHub repository
      description: Retrieve list of branches for a specific GitHub repository
      operationId: listRepositoryBranches
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: account_name
          in: path
          description: Name of the GitHub organization/user
          required: true
          type: string
        - name: repo_id
          in: path
          description: ID of the GitHub repository
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/BranchList"
        "404":
          description: Account or repository not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /azure/accounts/{org_id}/projects:
    get:
      tags:
        - Azure DevOps
        - Projects
      summary: List Azure DevOps projects
      description: Retrieve list of projects in an Azure DevOps organization
      operationId: listAzureProjects
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: org_id
          in: path
          description: Azure DevOps organization ID (GUID)
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/RepositoryList"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /azure/accounts/{org_id}/projects/{project_id}/repos:
    get:
      tags:
        - Azure DevOps
        - Projects
        - Repositories
      summary: List repositories in Azure DevOps project
      description: Retrieve list of repositories for a specific Azure DevOps project
      operationId: listAzureProjectRepositories
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: org_id
          in: path
          description: Azure DevOps organization ID (GUID)
          required: true
          type: string
        - name: project_id
          in: path
          description: ID of the Azure DevOps project
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/RepositoryList"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /azure/accounts/{org_id}/repos/{repo_id}/branches:
    get:
      tags:
        - Azure DevOps
        - Repositories
        - Branches
      summary: List branches in Azure DevOps repository
      description: Retrieve list of branches for a specific Azure DevOps repository. The project_id is derived automatically from the GitHubProjectRepo table.
      operationId: listAzureRepositoryBranches
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: org_id
          in: path
          description: Azure DevOps organization ID (GUID)
          required: true
          type: string
        - name: repo_id
          in: path
          description: ID of the Azure DevOps repository
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/BranchList"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /azure/accounts/{org_id}/projects/{project_id}/repos/{repo_id}/branches:
    get:
      tags:
        - Azure DevOps
        - Repositories
        - Branches
      summary: List branches in Azure DevOps repository with explicit project
      description: Retrieve list of branches for a specific Azure DevOps repository using explicit project and repository IDs
      operationId: listAzureRepositoryBranchesWithProject
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: org_id
          in: path
          description: Azure DevOps organization ID (GUID)
          required: true
          type: string
        - name: project_id
          in: path
          description: ID of the Azure DevOps project
          required: true
          type: string
        - name: repo_id
          in: path
          description: ID of the Azure DevOps repository
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/BranchList"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /github/webhook:
    post:
      tags:
        - stripe
      summary: Github webhook
      description: ""
      operationId: githubWebhook
      x-google-backend:
        address: https://archie-github-handler-************.us-central1.run.app
        path_translation: APPEND_PATH_TO_ADDRESS
        deadline: 15.0
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Status200"
        "401":
          description: "Unauthorized: Invalid Input"

  /_healthcheck:
    get:
      tags:
        - archie-service-markdown
      summary: Service for markdown
      description: ""
      operationId: markdown
      x-google-backend:
        address: https://archie-service-markdown-************.us-central1.run.app
        path_translation: APPEND_PATH_TO_ADDRESS
        deadline: 15.0
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Status200"
        "401":
          description: "Unauthorized: Invalid Input"
      security:
        - jwt_auth: []

  /pdf/convert:
    post:
      tags:
        - archie-service-markdown
      summary: Convert markdown to PDF
      description: ""
      operationId: markdownPdfDownload
      x-google-backend:
        address: https://archie-service-markdown-************.us-central1.run.app
        path_translation: APPEND_PATH_TO_ADDRESS
        deadline: 300.0
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Status200"
        "401":
          description: Unauthorized
      security:
        - jwt_auth: []

  /pdf/download:
    post:
      tags:
        - archie-service-markdown
      summary: Download PDF
      description: ""
      operationId: pdfDownload
      x-google-backend:
        address: https://archie-service-markdown-************.us-central1.run.app
        path_translation: APPEND_PATH_TO_ADDRESS
        deadline: 300.0
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Status200"
        "401":
          description: Unauthorized
      security:
        - jwt_auth: []

  /api-docs:
    get:
      tags:
        - Documentation
      summary: API Documentation
      description: Serves the Swagger UI interface for API documentation
      operationId: getApiDocs
      produces:
        - text/html
      responses:
        "200":
          description: HTML page containing Swagger UI
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /mermaid/validate:
    post:
      tags:
        - archie-service-markdown
      summary: Validate mermaid diagram
      description: Validates the syntax of a mermaid diagram
      operationId: validateMermaid
      x-google-backend:
        address: https://archie-service-markdown-************.us-central1.run.app
        path_translation: APPEND_PATH_TO_ADDRESS
        deadline: 15.0
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Validation result
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid request
          schema:
            $ref: "#/definitions/Status400"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"
      security:
        - jwt_auth: []

  /stripe/create-checkout-session:
    post:
      tags:
        - stripe
      summary: Stripe checkout session creation
      description: Create a Stripe checkout session to subscribe to a plan
      operationId: createStripeCheckoutSession
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Checkout session parameters
          required: true
          schema:
            type: object
            properties:
              project_id:
                type: string
                description: Optional project ID to associate with the subscription
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Status200"
        "401":
          description: "Unauthorized: Invalid Input"
      security:
        - jwt_auth: []

  /stripe/manage-subscription:
    post:
      tags:
        - stripe
      summary: Create Stripe portal session for subscription management
      description: Creates a Stripe billing portal session for managing an existing subscription
      operationId: createPortalSession
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Portal session created successfully
          schema:
            type: object
            properties:
              url:
                type: string
                description: URL to the Stripe billing portal
                example: https://billing.stripe.com/p/session/xyz
        "400":
          description: Invalid request
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: No subscription found for user
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"
      security:
        - jwt_auth: []

  /hubspot/contact:
    post:
      tags:
        - hubspot
      summary: Create or update HubSpot contact
      description: Creates or updates a contact in HubSpot using the provided information
      operationId: createOrUpdateHubspotContact
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Contact object that needs to be created or updated
          required: true
          schema:
            $ref: "#/definitions/ContactInput"
      responses:
        "200":
          description: Contact created/updated successfully
          schema:
            $ref: "#/definitions/Status200"
        "500":
          description: Error occurred while processing the request
          schema:
            $ref: "#/definitions/Status500"

  /job:
    post:
      tags:
        - jobs
      summary: Trigger a job
      description: ""
      operationId: triggerJob
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: JobRequest to be triggered
          required: true
          schema:
            $ref: "#/definitions/JobRequest"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/JobResult"
        "401":
          description: Authentication failed
      security:
        - jwt_auth: []

  /job/{job_id}:
    get:
      tags:
        - Job
      summary: Get job details
      description: Retrieve details of a specific job
      operationId: getJobById
      produces:
        - application/json
      parameters:
        - name: job_id
          in: path
          description: ID of the job
          required: true
          type: string
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Successful operation
        "400":
          description: Invalid job ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Job not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /health-check:
    get:
      tags:
        - utils
      summary: Health check
      description: An API for health check
      operationId: healthCheck
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Ok
        "401":
          description: Authentication failed
      security:
        - jwt_auth: []

  /project:
    get:
      tags:
        - project
      summary: Project endpoint GET requests
      operationId: projectGet
      produces:
        - application/json
      security:
        - jwt_auth: []
      responses:
        "200":
          description: ok
          schema:
            $ref: "#/definitions/GetProjectListOutput"
        "400":
          description: Invalid request
          schema:
            $ref: "#/definitions/Status400"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

    post:
      tags:
        - Project
      summary: Create a new project
      description: Creates a new project with the specified name
      operationId: createProject
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Project object that needs to be created
          required: true
          schema:
            $ref: "#/definitions/CreateProjectInput"
      responses:
        "201":
          description: Project created successfully
          schema:
            $ref: "#/definitions/CreateProjectOutput"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}:
    get:
      tags:
        - Project
      summary: Get project by ID
      description: Retrieves a specific project by its ID
      operationId: getProjectById
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project to retrieve
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetProjectDetailOutput"
        "400":
          description: Invalid project ID format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"
    put:
      tags:
        - Project
      summary: Update project
      description: Update an existing project by ID
      operationId: updateProject
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project to update
          required: true
          type: string
        - in: body
          name: body
          description: Updated project object
          required: true
          schema:
            $ref: "#/definitions/UpdateProjectInput"
      responses:
        "200":
          description: Project updated successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"
      security:
        - jwt_auth: []

    delete:
      tags:
        - Project
      summary: Delete project
      description: Delete a project by ID
      operationId: deleteProject
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project to delete
          required: true
          type: string
      responses:
        "200":
          description: Project deleted successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid project ID format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"
      security:
        - jwt_auth: []

  /project/{project_id}/github/repos:
    get:
      tags:
        - Project
        - GitHub
      summary: Get project GitHub repositories
      description: Retrieve GitHub repositories associated with a project
      operationId: getProjectGithubRepos
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/ProjectGithubRepoOutputList"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

    post:
      tags:
        - Project
        - GitHub
      summary: Onboard GitHub repositories
      description: Onboard input and output GitHub repositories for a project
      operationId: onboardGithubRepositories
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - in: body
          name: body
          description: Repository onboarding details
          required: true
          schema:
            $ref: "#/definitions/GithubRepoOnboardingInputList"
      responses:
        "200":
          description: Repositories onboarded successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/new/product/github/repos:
    post:
      tags:
        - Project
        - GitHub
      summary: Onboard GitHub repositories for new product
      description: Onboard input github repositories for new product
      operationId: onboardNewProductGithubRepositories
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - in: body
          name: body
          description: Repository onboarding details
          required: true
          schema:
            $ref: "#/definitions/NewProductGithubRepoOnboardingInputList"
      responses:
        "200":
          description: Repositories onboarded successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/job/status:
    get:
      tags:
        - Project
        - Job
      summary: Get project job statuses
      description: Retrieves the overall status and individual component statuses for a project's jobs
      operationId: getProjectJobStatus
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetProjectJobStatusOutput"
        "400":
          description: Invalid project ID format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/soft-req:
    get:
      tags:
        - Project
        - Software Requirements
      summary: Get software requirements
      description: Retrieves software requirements metadata for a specific project
      operationId: getSoftwareRequirements
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SoftwareRequirement"
        "400":
          description: Invalid project ID format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found or no requirements exist
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/soft-req/document:
    get:
      tags:
        - Project
        - Software Requirements
      summary: Get software requirements document URL
      description: Retrieves a presigned URL for accessing the software requirements document
      operationId: getSoftwareRequirementsDocument
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetSoftwareRequirementDocumentOutput"
        "400":
          description: Invalid project ID format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or document not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

    put:
      tags:
        - Project
        - Software Requirements
      summary: Upload software requirements document
      description: Upload a document file for software requirements
      operationId: uploadSoftwareRequirementsDocument
      consumes:
        - multipart/form-data
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: file
          in: formData
          description: Software requirements document file to upload
          required: true
          type: string
      responses:
        "200":
          description: Document uploaded successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid input or file format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "413":
          description: File size too large
          schema:
            $ref: "#/definitions/Status413"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/soft-req/document/approve:
    post:
      tags:
        - Project
        - Software Requirements
      summary: Submit software requirements document
      description: Submit the uploaded software requirements document for review
      operationId: submitSoftwareRequirementsDocument
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      responses:
        "200":
          description: Document submitted successfully
          schema:
            $ref: "#/definitions/TechSpecJobTriggerOutput"
        "400":
          description: Invalid request or no document found to submit
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/soft-req/job/status:
    get:
      tags:
        - Project
        - Software Requirements
        - Job
      summary: Get software requirements job status
      description: Retrieve the status and metadata of a software requirements job
      operationId: getSoftwareRequirementsJobStatus
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetSoftwareReqJobStatusOutput"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or job not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"
        "503":
          description: Job information is not available yet
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec:
    get:
      tags:
        - Project
        - Technical Specification
      summary: Get technical specifications
      description: Retrieve technical specifications for a project with version support
      operationId: getTechnicalSpecifications
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Successful operation
          schema:
            type: array
            items:
              $ref: "#/definitions/TechnicalSpec"
        "400":
          description: Invalid project ID or version
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/{tech_spec_id}:
    get:
      tags:
        - Project
        - Technical Specification
      summary: Get technical specification by ID
      description: Retrieve a specific technical specification by its ID with version support
      operationId: getTechnicalSpecificationById
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: tech_spec_id
          in: path
          description: ID of the technical specification
          required: true
          type: string
          default: latest
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/TechnicalSpec"
        "400":
          description: Invalid project ID, tech spec ID, or version
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or technical specification not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/{tech_spec_id}/document:
    get:
      tags:
        - Project
        - Technical Specification
      summary: Get technical specification document URL
      description: Retrieves a presigned URL for accessing the technical specification document
      operationId: getTechnicalSpecificationDocument
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: tech_spec_id
          in: path
          description: ID of the technical specification
          required: true
          type: string
          default: latest
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetTechnicalSpecificationDocumentOutput"
        "400":
          description: Invalid project ID format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or document not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

    put:
      tags:
        - Project
        - Technical Specification
      summary: Update technical specification document
      description: Updates a technical specification document by uploading a new document
      operationId: updateTechnicalSpecificationDocument
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: tech_spec_id
          in: path
          description: ID of the technical specification (use 'latest' for the most recent version)
          required: true
          type: string
        - name: file
          in: formData
          description: Technical specification document file to upload
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid project ID format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or document not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/{tech_spec_id}/document/approve:
    post:
      tags:
        - Project
        - Technical Specification
      summary: Submit technical specification for code generation
      description: Trigger code generation process for a technical specification
      operationId: submitTechSpecForCodeGeneration
      security:
        - jwt_auth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: tech_spec_id
          in: path
          description: ID of the technical specification
          required: true
          type: string
      responses:
        "200":
          description: Code generation process initiated successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid request parameters
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or technical specification not found
          schema:
            $ref: "#/definitions/Status404"
        "409":
          description: Job already in progress
          schema:
            $ref: "#/definitions/Status409"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/{tech_spec_id}/document/pdf:
    get:
      tags:
        - Project
        - Technical Specification
      summary: Get technical specification PDF
      description: Retrieve the PDF version of technical specification document
      operationId: getTechnicalSpecificationPDF
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: tech_spec_id
          in: path
          description: ID of the technical specification
          required: true
          type: string
          default: latest
      responses:
        "202":
          description: PDF generation in progress
          schema:
            $ref: "#/definitions/PDFInProgressResponse"
        "200":
          description: PDF ready for download
          schema:
            $ref: "#/definitions/PDFReadyResponse"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or technical specification not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/job/status:
    get:
      tags:
        - Project
        - Technical Specification
        - Job
      summary: Get technical specification job status
      description: Retrieve the status and metadata of a technical specification job
      operationId: getTechnicalSpecJobStatus
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetTechnicalSpecJobStatusOutput"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or job not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"
        "503":
          description: Job information is not available yet
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/prompt:
    post:
      tags:
        - Project
        - Technical Specification
      summary: Update technical specification prompt
      description: Update or create prompt for technical specification
      operationId: updateTechSpecPrompt
      consumes:
        - application/json
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - in: body
          name: body
          description: Prompt details
          required: true
          schema:
            $ref: "#/definitions/TechSpecPromptInput"
      responses:
        "200":
          description: Prompt updated successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/{tech_spec_id}/run/status:
    get:
      tags:
        - Project
        - Technical Specification
        - Job
      summary: Get technical specification job status
      description: Retrieve the status of a technical specification generation job
      operationId: getTechnicalSpecRunStatus
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: tech_spec_id
          in: path
          description: ID of the technical specification
          required: true
          type: string
          default: latest
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetTechnicalSpecJobStatusOutput"
        "400":
          description: Invalid project ID or job ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or job not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/revert:
    post:
      tags:
        - Project
        - Technical Specification
      summary: Revert technical specification
      description: Revert a technical specification to a previous version
      operationId: revertTechSpec
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Technical specification reverted successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or technical specification not found
          schema:
            $ref: "#/definitions/Status404"
        "409":
          description: Cannot revert (no previous version exists)
          schema:
            $ref: "#/definitions/Status409"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/sync/status:
    get:
      tags:
        - Project
        - Technical Specification
      summary: Get technical specification sync status
      description: Check if the technical specification is in sync with the current project state
      operationId: getTechSpecSyncStatus
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Successfully retrieved sync status
          schema:
            $ref: "#/definitions/TechSpecSyncStatusOutput"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

    post:
      tags:
        - Project
        - Technical Specification
      summary: Trigger technical specification sync
      description: Initiate a sync job for technical specification
      operationId: triggerTechSpecSync
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Sync job triggered successfully
          schema:
            $ref: "#/definitions/TechSpecSyncTriggerOutput"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "409":
          description: Sync job already in progress
          schema:
            $ref: "#/definitions/Status409"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/code-gen:
    get:
      tags:
        - Project
        - Code Generation
      summary: Get latest code generation
      description: Retrieves latest code generation metadata for a specific project
      operationId: getCodeGen
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/CodeGeneration"
        "400":
          description: Invalid project ID format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found or code is not generated yet
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/tech-spec/{tech_spec_id}/code-gen:
    get:
      tags:
        - Project
        - Code Generation
      summary: Get code generation by tech spec ID
      description: Retrieve a specific code gen by tech spec ID
      operationId: getCodeGenByTechSpecId
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: tech_spec_id
          in: path
          description: ID of the Technical Specification
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/CodeGeneration"
        "400":
          description: Invalid project ID format
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found or code is not generated yet
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/code-gen/{code_gen_id}/github/commit:
    get:
      tags:
        - Project
        - Code Generation
        - GitHub
      summary: Get code generation commit details
      description: Retrieve commit and pull request information for code generation
      operationId: getCodeGenCommit
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: code_gen_id
          in: path
          description: ID of the code generation record.
          required: true
          type: string
      security:
        - jwt_auth: []
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/CodeGenGithubCommitOutput"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/code-gen/{code_gen_id}/github/pr/action:
    post:
      security:
        - jwt_auth: []
      tags:
        - Project
        - Code Generation
        - GitHub
      summary: Perform PR action for a specific code generation
      description: >
        This endpoint triggers a merge or close action on the GitHub pull request associated with the specified code generation.
        Note: A PR can be accepted only once. If the PR has already been merged, further actions will return a conflict.
      operationId: performGithubPRActionByCodeGen
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project.
          required: true
          type: string
        - name: code_gen_id
          in: path
          description: ID of the code generation record.
          required: true
          type: string
        - in: body
          name: body
          description: Action to be performed on the pull request.
          required: true
          schema:
            $ref: "#/definitions/PRActionInput"
      responses:
        "200":
          description: PR action completed successfully.
          schema:
            $ref: "#/definitions/CodeGenGithubCommitOutput"
        "400":
          description: Invalid input.
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Code generation or pull request not found.
          schema:
            $ref: "#/definitions/Status404"
        "409":
          description: The pull request has already been accepted or is in a terminal state.
          schema:
            $ref: "#/definitions/Status409"
        "500":
          description: Internal server error.
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/code-gen/{code_gen_id}/github/pr/status:
    get:
      security:
        - jwt_auth: []
      tags:
        - Project
        - Code Generation
        - GitHub
      summary: Get code generation PR status
      description: Retrieve the status of the pull request associated with a code generation
      operationId: getCodeGenGithubPRStatus
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - name: code_gen_id
          in: path
          description: ID of the code generation record.
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/PRStatusOutput"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or code generation not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/code-gen/run/status:
    get:
      tags:
        - Project
        - Code Generation
        - Job
      summary: Get code generation job status
      description: Retrieve the status and metadata of a code generation job
      operationId: getCodeGenerationJobStatus
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetCodeGenJobStatusOutput"
        "400":
          description: Invalid project ID
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project or job not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"
        "503":
          description: Job information is not available yet
          schema:
            $ref: "#/definitions/Status500"

  /repository/{repository_id}/branches/{branch_name}:
    get:
      tags:
        - Project
      summary: Get platform-specific branch details
      description: >
        Retrieves Blitzy-specific details about a branch, including lock status
        that extends beyond standard Git information.
      operationId: getBranchesDetailsForRepository
      security:
        - jwt_auth: []
      produces:
        - application/json
      parameters:
        - name: repository_id
          in: path
          description: ID of the repository
          required: true
          type: string
        - name: branch_name
          in: path
          description: Name of the git branch
          required: true
          type: string
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GetBranchDetailsForRepositoryOutput"
        "400":
          description: Invalid repository ID or branch name
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Branch or project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /project/{project_id}/document/prompt:
    post:
      tags:
        - Document
      summary: Upload document prompt
      description: Uploads the document prompt to Google Cloud Storage for a specific project
      operationId: handleDocumentPromptSubmission
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: project_id
          in: path
          description: ID of the project
          required: true
          type: string
        - in: body
          name: body
          description: Document prompt details to be uploaded
          required: true
          schema:
            $ref: "#/definitions/DocumentPromptInput"
      responses:
        "200":
          description: Document prompt uploaded successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Project not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user:
    post:
      tags:
        - User
      summary: Create a new user
      description: Creates a new user with the specified user ID and email
      operationId: createUser
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: User object that needs to be created
          required: true
          schema:
            $ref: "#/definitions/CreateUserInput"
      responses:
        "201":
          description: User created successfully
          schema:
            $ref: "#/definitions/Status201"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "409":
          description: User already exists
          schema:
            $ref: "#/definitions/Status409"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user/profile:
    get:
      tags:
        - User
      summary: Get user info
      description: Retrieves a list of all users
      operationId: getUsers
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            type: array
            items:
              $ref: "#/definitions/User"
        "400":
          description: Invalid request
          schema:
            $ref: "#/definitions/Status400"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

    put:
      tags:
        - User
      summary: Update user profile
      description: Update the current user's profile information
      operationId: updateUserProfile
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Updated user profile information
          required: true
          schema:
            $ref: "#/definitions/UpdateProfileInput"
      responses:
        "200":
          description: Profile updated successfully
          schema:
            $ref: "#/definitions/User"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user/config:
    put:
      tags:
        - User
        - Configuration
      summary: Update user configuration
      description: Update user's configuration settings including notifications and platform preferences
      operationId: updateUserConfig
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: User configuration settings to update
          required: true
          schema:
            $ref: "#/definitions/UpdateUserConfigInput"
      responses:
        "200":
          description: Configuration updated successfully
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/Status401"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user/geolocation:
    get:
      tags:
        - User
      summary: Get user geolocation
      description: Retrieves the current user's geolocation information
      operationId: getUserGeolocation
      security:
        - jwt_auth: []
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation (returns empty object if no geolocation exists)
          schema:
            $ref: "#/definitions/GeoLocation"
        "400":
          description: Invalid request
          schema:
            $ref: "#/definitions/Status400"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user/reconcile:
    post:
      tags:
        - User
      summary: Reconcile missing users to the backend.
      description: Retrieves a list of all users
      operationId: reconcileUsers
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            type: array
            items:
              $ref: "#/definitions/Status200"
        "400":
          description: Invalid request
          schema:
            $ref: "#/definitions/Status400"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user/github/install:
    post:
      tags:
        - User
        - GitHub
      summary: Save GitHub installation.
      description: Save user's GitHub installation information.
      operationId: saveGithubInstallInfo
      consumes:
        - application/json
      produces:
        - application/json
      security:
        - jwt_auth: []
      parameters:
        - in: body
          name: body
          description: GitHub installation details.
          required: true
          schema:
            $ref: "#/definitions/SaveGithubInstallationInfo"
      responses:
        "200":
          description: Installation saved successfully.
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid input
          schema:
            $ref: "#/definitions/Status400"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/Status401"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user/verification/send:
    post:
      tags:
        - User
      summary: Send user verification email
      description: Send verification email via firebase
      operationId: userVerificationSend
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Status200"
        "400":
          description: Invalid request
          schema:
            $ref: "#/definitions/Status400"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user/verification/status:
    get:
      tags:
        - User
      summary: Get user verification status
      description: Checks whether user is verified with firebase or not.
      operationId: userVerificationStatus
      security:
        - jwt_auth: []
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Status200"
        "401":
          description: Unauthorised if user is not verified
          schema:
            $ref: "#/definitions/Status401"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

  /user/subscription/status:
    get:
      tags:
        - User
        - Subscription
      summary: Get user subscription status
      description: Retrieves the subscription status for the current user
      operationId: getUserSubscriptionStatus
      security:
        - jwt_auth: []
      produces:
        - application/json
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Subscription"
        "400":
          description: Invalid request
          schema:
            $ref: "#/definitions/Status400"
        "404":
          description: Subscription not found
          schema:
            $ref: "#/definitions/Status404"
        "500":
          description: Internal server error
          schema:
            $ref: "#/definitions/Status500"

securityDefinitions:
  jwt_auth:
    authorizationUrl: ""
    flow: implicit
    type: oauth2
    x-google-issuer: <EMAIL>
    x-google-jwks_uri: https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>
    x-google-audiences: https://os.api.blitzy.dev/v1

definitions:
  Status200:
    type: object
    properties:
      message:
        type: string
        example: Ok.

  Status201:
    type: object
    properties:
      id:
        type: string
        example: f8759a72-8487-4881-b068-2af222342842
      message:
        type: string
        example: Created Successfully.

  Status400:
    type: object
    properties:
      message:
        type: string
        example: Bad Request.

  Status401:
    type: object
    properties:
      message:
        type: string
        example: Invalid email or password

  Status404:
    type: object
    properties:
      message:
        type: string
        example: Not Found.

  Status409:
    type: object
    properties:
      message:
        type: string
        example: Conflict.

  Status413:
    type: object
    properties:
      message:
        type: string
        example: File size exceeds maximum limit.

  Status429:
    type: object
    properties:
      message:
        type: string
        example: Limit exhausted

  Status500:
    type: object
    properties:
      message:
        type: string
        example: Internal Server Error.

  Status503:
    type: object
    properties:
      message:
        type: string
        example: Service not available yet.

  PromptStatus:
    type: string
    enum: [DRAFT, SUBMITTED]
    description: Status of the project prompt

  Status:
    type: string
    enum:
      [
        TODO,
        CREATED,
        GITHUB_PENDING,
        GITHUB_COMPLETED,
        PROMPT_PENDING,
        PROMPT_SUBMITTED,
        COMPLETED,
        QUEUED,
        IN_PROGRESS,
        DONE,
        SUBMITTED,
        FAILED,
      ]
    description: Status of the item

  ContactInput:
    type: object
    required:
      - email
    properties:
      email:
        type: string
        description: Email address of the contact
        format: email
      first_name:
        type: string
        description: First name of the contact
      last_name:
        type: string
        description: Last name of the contact
      company:
        type: string
        description: Company name of the contact
      phone:
        type: string
        description: Phone number of the contact
      plan:
        type: string
        description: Subscription plan type (Free/Pro/Enterprise)
        enum: [FREE, PRO, ENTERPRISE]
      utm_source:
        type: string
        description: UTM source parameter from marketing campaigns
      utm_medium:
        type: string
        description: UTM medium parameter from marketing campaigns
      utm_campaign:
        type: string
        description: UTM campaign parameter from marketing campaigns
      utm_id:
        type: string
        description: UTM ID parameter from marketing campaigns

  CreateProjectInput:
    type: object
    required:
      - name
      - type
    properties:
      name:
        type: string
        maxLength: 255
        description: Name of the project
      type:
        $ref: "#/definitions/ProjectInitialType"
        description: Type of the repository

  CreateProjectOutput:
    type: object
    required:
      - id
    properties:
      id:
        type: string
        maxLength: 255
        description: ID of the created project

  UpdateProjectInput:
    type: object
    required:
      - name
      - prompt
    properties:
      name:
        type: string
        maxLength: 255
        description: Name of the project

  ProjectInitialType:
    type: string
    enum:
      - NEW_PRODUCT
      - EXISTING_PRODUCT
    description: Project initial type

  Project:
    type: object
    properties:
      id:
        type: string
        description: Unique identifier of the project
      name:
        type: string
        maxLength: 255
        description: Name of the project
      prompt:
        type: string
        description: Project prompt.
      initialType:
        $ref: "#/definitions/ProjectInitialType"
        description: Type of the repository
      status:
        $ref: "#/definitions/Status"
        description: Current status of the project
      repoPrefix:
        type: string
        description: Prefix used for generating repository URL
      isDisabled:
        type: boolean
        description: Whether project is disabled.
      disableReason:
        type: string
        description: Reason behind disabling project.
      owner:
        type: string
        description: Owner of the project
      createdAt:
        type: integer
        description: Timestamp when the project was created
      updatedAt:
        type: integer
        description: Timestamp when the project was last updated

  GetAllProject:
    type: object
    properties:
      id:
        type: string
        description: Unique identifier of the project
      name:
        type: string
        maxLength: 255
        description: Name of the project
      status:
        $ref: "#/definitions/Status"
        description: Current status of the project
      repoPrefix:
        type: string
        description: Prefix used for generating repository URL
      promptStatus:
        type: string
        maxLength: 50
        description: Status of the prompt
      promptUpdatedAt:
        type: integer
        description: Timestamp when the prompt was last updated
      isDisabled:
        type: boolean
        description: Whether project is disabled.
      disableReason:
        type: string
        description: Reason behind disabling project.
      owner:
        type: string
        description: Owner of the project
      repoUrl:
        type: string
        description: Repo name of the project
      createdAt:
        type: integer
        description: Timestamp when the project was created
      updatedAt:
        type: integer
        description: Timestamp when the project was last updated

  GetProjectListOutput:
    type: object
    properties:
      projects:
        type: array
        items:
          $ref: "#/definitions/GetAllProject"

  Tokens:
    type: object
    properties:
      access_token:
        type: string

  JobRequest:
    type: object
    properties:
      prompt:
        type: string
      prefix:
        type: string

  JobResult:
    type: object
    properties:
      repo_name:
        type: string
      github_url:
        type: string

  User:
    type: object
    properties:
      id:
        type: string
        description: Unique identifier of the user
      firstName:
        type: string
        maxLength: 100
        description: First name of the user
      lastName:
        type: string
        maxLength: 100
        description: Last name of the user
      email:
        type: string
        maxLength: 255
        format: email
        description: Email address of the user
      company:
        type: string
        maxLength: 255
        description: Company name associated with the user
      avatarBlob:
        type: string
        description: User's avatar image data
      authMechanism:
        type: string
        maxLength: 50
        description: Authentication mechanism used by the user
      isVerified:
        type: boolean
        description: Whether user is verified
      isGithubAuthenticated:
        type: boolean
        description: Whether user has authenticated Blitzy app with github
      subscription:
        $ref: "#/definitions/Subscription"
        description: User's subscription details
      userConfig:
        $ref: "#/definitions/UserConfig"
        description: User configuration settings
      registrationCompleted:
        type: boolean
        description: Whether user's registration is completed or not
      createdAt:
        type: integer
        description: Timestamp when the user was created
      updatedAt:
        type: integer
        description: Timestamp when the user was last updated

  UpdateProfileInput:
    type: object
    properties:
      firstName:
        type: string
        minLength: 1
        maxLength: 100
        description: First name of the user
      lastName:
        type: string
        minLength: 1
        maxLength: 100
        description: Last name of the user
      company:
        type: string
        minLength: 1
        maxLength: 100
        description: Company name associated with the user
      avatarBlob:
        type: string
        description: User's avatar image data

  Subscription:
    type: object
    properties:
      stripeCustomerId:
        type: string
        description: Customer ID provided by Stripe
      stripeSubscriptionId:
        type: string
        description: Subscription ID provided by Stripe
      planName:
        type: string
        description: Name of the subscription plan
      startDate:
        type: integer
        description: Start date of the subscription
      endDate:
        type: integer
        description: End date of the subscription
      trialStartDate:
        type: integer
        description: Trial start date for the subscription
      trialEndDate:
        type: integer
        description: Trial end date for the subscription
      currentPeriodStartDate:
        type: integer
        description: Current period start date for the subscription
      currentPeriodEndDate:
        type: integer
        description: Current period end date for the subscription
      status:
        type: string
        maxLength: 50
        description: Current status of the subscription
      is_trialing:
        type: boolean
        default: false
        description: Indicates if the subscription is currently in trial period
      has_trial_received:
        type: boolean
        default: false
        description: Indicates if the subscription has ever received a trial

  SoftwareRequirement:
    type: object
    properties:
      id:
        type: string
        description: Unique identifier of the software requirement
      status:
        $ref: "#/definitions/Status"
        description: Current status of the software requirement
      requirements:
        type: string
        description: Detailed requirements text
      approvedAt:
        type: integer
        description: Timestamp when the requirements were approved
      link:
        type: string
        maxLength: 255
        description: Link to the requirements document
      jobId:
        type: string
        description: Job ID used for tracking
      storageType:
        type: string
        maxLength: 50
        description: Type of storage used for the requirements
      createdAt:
        type: integer
        description: Timestamp when the software requirement was created
      updatedAt:
        type: integer
        description: Timestamp when the software requirement was last updated

  CodeGeneration:
    type: object
    properties:
      id:
        type: string
        description: Unique identifier of the code generation record.
        example: cg_12345
      status:
        $ref: "#/definitions/Status"
        description: Current status of the code generation
      tech_spec_id:
        type: string
        description: Link to the specific Technical Specification version.
        example: ts_54321
      project_run_id:
        type: string
        description: Foreign key linking to the associated project run.
        example: run_98765
      code_repo_url:
        type: string
        description: URL of the generated code repository.
        example: https://github.com/org/repo
      metadata:
        type: object
        description: Additional metadata related to the code generation.
        additionalProperties: true
      ready_at:
        type: string
        format: date-time
        description: Timestamp when the code generation was marked as ready.
        example: "2025-02-23T15:30:00Z"

  TechnicalSpec:
    type: object
    properties:
      id:
        type: string
        description: Unique identifier of the technical specification
      jobType:
        $ref: "#/definitions/TechSpecJobType"
      status:
        $ref: "#/definitions/Status"
        description: Current status of the technical specification
      approvedAt:
        type: integer
        description: Timestamp when the specification was approved
      link:
        type: string
        maxLength: 255
        description: Link to the technical specification
      storageType:
        type: string
        maxLength: 50
        description: Type of storage used for the specification
      jobId:
        type: string
        description: Job ID used for tracking
      pdfReportStatus:
        type: string
        description: Status on report
      version:
        type: integer
        description: Version of the technical specification
        example: "1"
      createdAt:
        type: integer
        description: Timestamp when the technical specification was created
      updatedAt:
        type: integer
        description: Timestamp when the technical specification was updated

  GetProjectDetailOutput:
    allOf:
      - $ref: "#/definitions/Project"
      - type: object
    properties:
      technicalSpecAvailable:
        type: boolean
        description: Technical specification information available or not
        default: false
      codeGenerationAvailable:
        type: boolean
        description: Code generation information available or not
        default: false

  JobComponent:
    type: object
    properties:
      jobId:
        type: string
        description: Unique identifier of the job
      status:
        type: string
        description: Status of the job
        example: QUEUED
      metadata:
        $ref: "#/definitions/JobMetadata"
        description: Metadata about job

  JobMetadata:
    type: object
    properties:
      order:
        type: number
        description: order of document in document-generator workflow
      propagate:
        type: boolean
        description: whether to trigger the next step in the workflow
      repo_name:
        type: string
        description: assigned repo name
      current_index:
        type: number
        description: current step index
      total_steps:
        type: number
        description: total steps

  GetProjectJobStatusOutput:
    type: object
    properties:
      status:
        type: string
        description: Overall status of the project jobs
        example: IN_PROGRESS

  CreateUserInput:
    type: object
    required:
      - userId
      - email
    properties:
      userId:
        type: string
        description: Unique identifier for the user
      email:
        type: string
        description: Email address of the user
        format: email

  GetSoftwareRequirementDocumentOutput:
    type: object
    properties:
      preSignedURL:
        type: string
        description: Presigned URL for accessing the document
        example: https://storage-bucket.example.com/path/to/document?token=xyz
      expiryInSeconds:
        type: integer
        description: Time in which this URL will be expired

  GetTechnicalSpecificationDocumentOutput:
    type: object
    properties:
      preSignedURL:
        type: string
        description: Presigned URL for accessing the document
        example: https://storage-bucket.example.com/path/to/document?token=xyz
      expiryInSeconds:
        type: integer
        description: Time in which this URL will be expired

  LoginInput:
    type: object
    required:
      - email
      - password
    properties:
      email:
        type: string
        description: User's email address
        minLength: 1
      password:
        type: string
        description: User's password
        minLength: 1

  LoginOutput:
    type: object
    properties:
      displayName:
        type: string
        description: Display name of the user
      email:
        type: string
        format: email
        description: User's email address
      expiresIn:
        type: string
        description: Token expiration time in seconds
        example: "3600"
      idToken:
        type: string
        description: ID token for authentication
      kind:
        type: string
        description: Type of authentication response
      localId:
        type: string
        description: Local identifier for the user
      refreshToken:
        type: string
        description: Token used to refresh the authentication
      registered:
        type: boolean
        description: Indicates if the user is registered
        default: true

  RegisterUserInput:
    type: object
    required:
      - email
      - password
      - firstName
      - lastName
      - company
    properties:
      email:
        type: string
        description: User's email address
        minLength: 3
      password:
        type: string
        description: User's password
        minLength: 8
      firstName:
        type: string
        description: User's first name
        minLength: 1
      lastName:
        type: string
        description: User's last name
        minLength: 1
      company:
        type: string
        description: User's company
        minLength: 1
      emailVerified:
        type: boolean
        description: Whether to skip user verification
        default: false
      avatarBlob:
        type: string
        description: User's avatar image data
      utm_source:
        type: string
        description: UTM source parameter from marketing campaigns
      utm_medium:
        type: string
        description: UTM medium parameter from marketing campaigns
      utm_campaign:
        type: string
        description: UTM campaign parameter from marketing campaigns
      utm_id:
        type: string
        description: UTM ID parameter from marketing campaigns

  CreateStripeCheckoutResponse:
    type: object
    properties:
      url:
        type: string

  GetSoftwareReqJobStatusOutput:
    type: object
    properties:
      jobMetadata:
        $ref: "#/definitions/SoftwareReqJobMetadata"
      status:
        $ref: "#/definitions/Status"
        description: Current status of the software req job
      createdAt:
        type: integer
        description: Timestamp when the job was created
      updatedAt:
        type: integer
        description: Timestamp when the job was last updated

  SoftwareReqJobMetadata:
    type: object
    properties:
      order:
        type: integer
        description: Order of the job
      repoName:
        type: string
        description: Name of the repository

  GetTechnicalSpecJobStatusOutput:
    type: object
    properties:
      jobMetadata:
        $ref: "#/definitions/TechnicalSpecJobMetadata"
      status:
        $ref: "#/definitions/Status"
        description: Current status of the technical spec job
      createdAt:
        type: integer
        description: Timestamp when the job was created
      updatedAt:
        type: integer
        description: Timestamp when the job was last updated

  TechnicalSpecJobMetadata:
    type: object
    properties:
      order:
        type: integer
        description: Order of the job
      repoName:
        type: string
        description: Name of the repository
      totalSteps:
        type: integer
        description: Total steps in the job
      currentIndex:
        type: integer
        description: Current step index in the job

  GetCodeGenJobStatusOutput:
    type: object
    properties:
      jobMetadata:
        $ref: "#/definitions/CodeGenJobMetadata"
      status:
        $ref: "#/definitions/Status"
        description: Current status of the code gen job
      createdAt:
        type: integer
        description: Timestamp when the job was created
      updatedAt:
        type: integer
        description: Timestamp when the job was last updated

  CodeGenJobMetadata:
    type: object
    properties:
      repoUrl:
        type: string
        description: Order of the job
      repoName:
        type: string
        description: Name of the repository

  SaveGithubInstallationInfo:
    type: object
    required:
      - setupAction
      - code
    properties:
      code:
        type: string
        description: GitHub returned oauth code for the user
        minLength: 1
      installationID:
        type: string
        description: GitHub installationId
      setupAction:
        type: string
        description: GitHub setup type
        minLength: 1

  CreateStripeManageSubscriptionResponse:
    type: object
    properties:
      url:
        type: string

  GithubInstallationType:
    type: string
    enum:
      - ORGANIZATION
      - USER
    description: Type of GitHub installation

  GithubInstallationStatus:
    type: string
    enum:
      - PENDING
      - ACTIVE
      - REJECTED
      - UNINSTALLED
      - SUSPENDED
    description: Status of GitHub installation

  GetGithubInstallationList:
    type: object
    description: List of github installations
    properties:
      items:
        type: array
        items:
          $ref: "#/definitions/GetGithubInstallationOutput"

  GetGithubInstallationOutput:
    type: object
    properties:
      status:
        $ref: "#/definitions/GithubInstallationStatus"
        description: Current status of the installation
      requiresApproval:
        type: boolean
        description: Whether installation requires approval
      createdAt:
        type: integer
        description: Timestamp when the record was created
        example: **********
      updatedAt:
        type: integer
        description: Timestamp when the record was last updated
        example: **********
      installation_id:
        type: integer
        description: Github Installation ID
        example: ********
      installation_type:
        type: string
        description: Type of Github Installation
        example: USER
      target_name:
        type: string
        description: Account name
        example: account-name

  GetGitInstallationsOutputItem:
    type: object
    properties:
      status:
        $ref: "#/definitions/GithubInstallationStatus"
        description: Current status of the installation
      requiresApproval:
        type: boolean
        description: Whether installation requires approval
      createdAt:
        type: integer
        description: Timestamp when the record was created
        example: **********
      updatedAt:
        type: integer
        description: Timestamp when the record was last updated
        example: **********
      installation_id:
        type: string
        description: Git unique Installation ID, it is the same for all users from one organization
        example: ********
      installation_type:
        type: string
        description: Type of Github Installation
        example: USER
      target_name:
        type: string
        description: Account name
        example: account-name
      svc_type:
        $ref: "#/definitions/SVCType"
        description: Service type - either Azure DevOps or GitHub

  GetGitInstallationsOutput:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: "#/definitions/GetGitInstallationsOutputItem"
        description: List of git installations

  GetGithubProjectRepositoryOutput:
    type: object
    properties:
      repoId:
        type: string
        maxLength: 100
        description: GitHub repository ID
      repoName:
        type: string
        maxLength: 255
        description: Name of the repository
      repoFullName:
        type: string
        maxLength: 255
        description: Full name of the repository (owner/repo)
      status:
        type: string
        description: Current status of the repository

  GeoLocation:
    type: object
    properties:
      city:
        type: string
        description: City name
      country_code:
        type: string
        description: Two letter country code
      region:
        type: string
        description: Region or state name
      timezone:
        type: string
        description: Timezone identifier

  PDFInProgressResponse:
    type: object
    properties:
      status:
        type: string
        description: Current status of PDF generation
        example: Output could be one of [Queued, InProgress, Ready]

  PDFReadyResponse:
    type: object
    properties:
      preSignedURL:
        type: string
        description: Presigned URL to download the PDF
        example: https://storage.example.com/documents/tech-spec.pdf?token=xyz
      expiryInSeconds:
        type: integer
        description: Time in seconds until the presigned URL expires
        example: 3600

  AccountType:
    type: string
    enum:
      - Organization
      - User
    description: Type of GitHub account

  GithubAccountBasicList:
    type: object
    properties:
      results:
        type: array
        items:
          $ref: "#/definitions/GithubAccountBasic"

  GithubAccountBasic:
    type: object
    properties:
      id:
        type: string
        description: ID of the GitHub account
        example: 12345
      name:
        type: string
        description: Name of the GitHub account
        example: microsoft
      type:
        $ref: "#/definitions/AccountType"
        description: Type of the account
        example: Organization
      installationId:
        type: string
        description: Installation ID used to fetch this org
        example: Organization

  RepositoryList:
    type: object
    properties:
      results:
        type: array
        items:
          $ref: "#/definitions/Repository"

  Repository:
    type: object
    properties:
      id:
        type: string
        description: Unique identifier of the repository
      name:
        type: string
        description: Name of the repository
        example: repo-name
      fullName:
        type: string
        description: Full name of the repository including owner
        example: microsoft/repo-name
      private:
        type: boolean
        description: Whether the repository is private
        example: true

  BranchList:
    type: object
    properties:
      results:
        type: array
        items:
          $ref: "#/definitions/Branch"

  Branch:
    type: object
    properties:
      name:
        type: string
        description: Name of the branch
        example: main
      protected:
        type: boolean
        description: Whether the branch is protected
        example: true

  RepoType:
    type: string
    enum:
      - SOURCE
      - TARGET
    description: Type of repository (Input or Output)

  ProjectGithubRepoList:
    type: object
    properties:
      results:
        type: array
        items:
          $ref: "#/definitions/ProjectGithubRepo"

  ProjectGithubRepo:
    type: object
    properties:
      type:
        $ref: "#/definitions/RepoType"
        description: Type of the repository
      orgName:
        type: string
        description: Name of the GitHub organization
        example: microsoft
      repoId:
        type: string
        description: ID of the repository
        example: "123456789"
      branchName:
        type: string
        description: Name of the branch
        example: main
      createBranch:
        type: boolean
        description: Whether to create a new branch
        example: false
      needsScan:
        type: boolean
        description: Whether scan is required
        example: false

  ProjectGithubRepoOutputList:
    type: object
    properties:
      results:
        type: array
        items:
          $ref: "#/definitions/ProjectGithubRepoOutput"

  ProjectGithubRepoOutput:
    type: object
    properties:
      type:
        $ref: "#/definitions/RepoType"
        description: Type of the repository
      orgName:
        type: string
        description: Name of the GitHub organization
        example: microsoft
      repoName:
        type: string
        description: Name of the GitHub repository
        example: backend
      repoId:
        type: string
        description: ID of the repository
        example: "123456789"
      branchName:
        type: string
        description: Name of the branch
        example: main
      needsScan:
        type: boolean
        description: Whether scan is required
        example: false

  NewProductGithubRepoOnboardingInputList:
    type: object
    required:
      - input
    properties:
      input:
        $ref: "#/definitions/NewProductGithubRepoOnboardingInput"

  NewProductGithubRepoOnboardingInput:
    type: object
    required:
      - type
      - orgName
      - repoName
    properties:
      orgName:
        type: string
        description: Name of the GitHub organization
        example: microsoft
      repoId:
        type: string
        description: ID of the repository
        example: "123456789"
      repoName:
        type: string
        description: Name of the repository
        example: test-repo
      azureProjectId:
        type: string
        description: ID of the project in azure
        example: test-project
      azureOrgId:
        type: string
        description: ID of the org in azure
        example: test-org
      createRepo:
        type: boolean
        description: Whether to create repository
        default: false

  GithubRepoOnboardingInputList:
    type: object
    required:
      - input
    properties:
      input:
        type: array
        items:
          $ref: "#/definitions/GithubRepoOnboardingInput"

  GithubRepoOnboardingInput:
    type: object
    required:
      - type
      - orgName
      - repoName
    properties:
      type:
        $ref: "#/definitions/RepoType"
        description: Type of the repository
      installationId:
        type: string
        description: Github app ID.
        example: 1234567
      orgName:
        type: string
        description: Name of the GitHub organization
        example: microsoft
      repoId:
        type: string
        description: ID of the repository
        example: "123456789"
      repoName:
        type: string
        description: Name of the repository
        example: test-repo
      branchName:
        type: string
        description: Name of the branch
        example: main
      azureProjectId:
        type: string
        description: ID of the azure project
        example: "123"
      azureOrgId:
        type: string
        description: OD of the azure org
        example: "123"
      createRepo:
        type: boolean
        description: Whether to create repository
        default: false

  TechSpecJobType:
    type: string
    enum:
      - NEW_PRODUCT
      - EXISTING_PRODUCT
      - ADD_FEATURE
      - REFACTOR_CODE
      - SYNC_TECH_SPEC
      - FIX_BUGS
      - FIX_CVES
      - ADD_TESTING
      - DOCUMENT_CODE
      - CUSTOM
    description: Type of the technical specification prompt

  TechSpecPromptInput:
    type: object
    required:
      - type
    properties:
      prompt:
        type: string
        description: The technical specification prompt text
      isDraft:
        type: boolean
        description: Whether the prompt is in draft state
        example: true
        default: false
      type:
        $ref: "#/definitions/TechSpecJobType"
        description: Type of the prompt

  DocumentPromptInput:
    type: object
    properties:
      prompt:
        type: string
        description: The document prompt text

  TechSpecSyncStatusOutput:
    type: object
    properties:
      needsScan:
        type: boolean
        description: Whether the technical specification is in sync with the project
        example: true

  TechSpecSyncTriggerOutput:
    type: object
    properties:
      jobId:
        type: string
        description: ID of the triggered sync job
        example: sync-job-123

  TechSpecJobTriggerOutput:
    type: object
    properties:
      jobId:
        type: string
        description: ID of the triggered sync job
        example: sync-job-123

  CommitStatus:
    type: string
    enum:
      - PENDING
      - MERGED
      - REJECTED
      - CLOSED
      - DONE
    description: Status of the commit

  PRAction:
    type: string
    enum:
      - NOT_TAKEN
      - CLOSED
      - OPEN
    description: Action taken on the pull request

  SVCType:
    type: string
    enum:
      - GITHUB
      - AZURE_DEVOPS
    description: Type of version control system

  CodeGenGithubCommitOutput:
    type: object
    properties:
      id:
        type: string
        description: Unique identifier of the commit record
      projectRunId:
        type: string
        description: Associated project run identifier
      codeGenId:
        type: string
        description: Associated code gen identifier
      orgName:
        type: string
        description: Name of the GitHub organization
      repoName:
        type: string
        description: Name of the repository
      repoId:
        type: string
        description: Repository identifier
      branchName:
        type: string
        description: Branch name
      blitzyCommitHash:
        type: string
        description: Hash of the commit
      blitzyCommitUrl:
        type: string
        description: URL of the commit
      blitzyBranchUrl:
        type: string
        description: URL of the github branch
      status:
        $ref: "#/definitions/CommitStatus"
        description: Current status of the commit
      prAction:
        $ref: "#/definitions/PRAction"
        description: Action taken on the pull request
      originalHeadCommitHash:
        type: string
        description: Original head commit reference
      prNumber:
        type: integer
        description: Pull request number
      prLink:
        type: string
        description: Link to the pull request
      metadata:
        type: object
        description: Additional metadata
        additionalProperties: true
      createdAt:
        type: string
        format: date-time
        description: Timestamp when the record was created
      resolvedAt:
        type: string
        format: date-time
        description: Timestamp when the commit was resolved

  PRActionInput:
    type: object
    required:
      - action
    properties:
      action:
        type: string
        description: >
          Action to be performed on the pull request.
          Use "MERGE" to merge the PR or "CLOSE" to close it without merging.
        enum: [MERGE, CLOSE]
        example: MERGE

  UserConfig:
    type: object
    properties:
      techSpecNotificationEnabled:
        type: boolean
        description: Whether technical specification notifications are enabled
        example: true
      codeGenNotificationEnabled:
        type: boolean
        description: Whether code generation notifications are enabled
        example: false
      platformConfig:
        type: object
        description: Platform-specific configuration settings
        additionalProperties: true
        example: { theme: dark, dashboard_layout: grid, auto_save: true }

  UpdateUserConfigInput:
    type: object
    properties:
      techSpecNotificationEnabled:
        type: boolean
        description: Whether technical specification notifications are enabled
      codeGenNotificationEnabled:
        type: boolean
        description: Whether code generation notifications are enabled
      platformConfig:
        type: object
        description: Platform-specific configuration settings
        additionalProperties: true

  GetBranchDetailsForRepositoryOutput:
    type: object
    properties:
      locked:
        type: boolean
        description: Whether branch is locked for any tech-spec jobs

  GetAzureAuthURL:
    type: object
    properties:
      authUrl:
        type: string
        description: URL we user have to be redirected for authethication

  PRStatusOutput:
    type: object
    properties:
      status:
        $ref: "#/definitions/CommitStatus"
        description: Status of the pull request
